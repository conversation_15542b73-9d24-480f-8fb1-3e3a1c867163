/*
 * @description: 导出文件
 * @fileName: useExportFile.ts
 * @author: shenname <<EMAIL>>
 * @date: 2025-03-18 10:01:01
 * @version: v1.0.0
 */
export function useExportFile() {
    const downloadFile = (content: string, fileName: string, mimeType: string) => {
        const blob = new Blob([content], { type: mimeType })
        const url = URL.createObjectURL(blob)

        const a = document.createElement('a')
        a.href = url
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
    }

    /**
     * 导出 JSON 文件
     * @param data 要导出的数据
     * @param fileName 文件名（默认：data.json）
     */
    const exportJson = (data: any, fileName: string = 'data.json') => {
        const content = JSON.stringify(data, null, 2)
        downloadFile(content, fileName, 'application/json')
    }

    /**
     * 导出 CSV 文件
     * @param data 要导出的数据（数组格式）
     * @param fileName 文件名（默认：data.csv）
     */
    const exportCsv = (data: any[], fileName: string = 'data.csv') => {
        const headers = Object.keys(data[0]).join(',')
        const rows = data.map((item) => Object.values(item).join(','))
        const content = [headers, ...rows].join('\n')
        downloadFile(content, fileName, 'text/csv')
    }

    /**
     * 导出 Excel 文件（基于 CSV）
     * @param data 要导出的数据（数组格式）
     * @param fileName 文件名（默认：data.xlsx）
     */
    const exportExcel = (data: any[], fileName: string = 'data.xlsx') => {
        const headers = Object.keys(data[0]).join(',')
        const rows = data.map((item) => Object.values(item).join(','))
        const content = [headers, ...rows].join('\n')
        downloadFile(content, fileName, 'application/vnd.ms-excel')
    }

    return {
        exportJson,
        exportCsv,
        exportExcel
    }
}
