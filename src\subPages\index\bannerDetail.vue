<template>
    <u-navbar
        title="详情"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <text class="title">{{ data.title }}</text>
        <mp-html :content="data.content" />
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { bannerDetail } from '@/api/index/index'
import { onLoad } from '@dcloudio/uni-app'

const data = ref({})
onLoad(async (e) => {
    await bannerDetail(e.id).then((res) => {
        res.data.content = res.data.content
            .replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
            .replace(/<p([^>]*)>/g, function (match, p1) {
                if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                    // 如果已有background-color定义，先移除再追加（避免重复）
                    return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
                } else if (/style\s*=\s*['"]/.test(p1)) {
                    // 有style但无background-color，直接在末尾追加
                    return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
                } else {
                    // 无style属性，直接添加
                    return '<p' + p1 + ' style="background-color: transparent!important;">'
                }
            })
            .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
        data.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    height: calc(100vh - 174rpx);
    background: #0e2942;
    display: grid;
    align-content: flex-start;
    box-sizing: border-box;
    padding: 20rpx 30rpx;
    justify-items: center;

    .title {
        font-family: Source Han Sans;
        font-size: 40rpx;
        font-weight: bold;
        line-height: normal;
        letter-spacing: 2rpx;
        color: #ffffff;
        margin-bottom: 40rpx;
    }
}
</style>
