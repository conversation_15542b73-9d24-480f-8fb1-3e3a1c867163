<script setup lang="ts">
import { orderDetail, courseRefund } from '@/api/course/index'
import { payInfo, userPay } from '@/api/common/common'
import { getSevenDaysBeforeCutoff, getImageUrl } from '@/utils/common'
const details = ref<any>({})
const getDetail = (id: number) => {
    orderDetail(id).then((res) => {
        const { data } = res as { data: { [n: string]: string } }
        console.log(data)
        details.value = data
    })
}

const pay = () => {
    payInfo('course', details.value.order_id).then((res) => {
        console.log(1231312, res)
        const { data } = res as { data: { trade_id: number; trade_type: string } }
        userPay({ type: 'wechatpay', trade_id: data.trade_id, trade_type: data.trade_type }).then((res) => {
            const { data: payData } = res as { data: { [n: string]: string } }
            uni.requestPayment({
                provider: 'wxpay',
                timeStamp: payData.timeStamp,
                nonceStr: payData.nonceStr,
                package: payData.package,
                signType: payData.signType,
                paySign: payData.paySign,
                success: function (res) {
                    console.log('success:' + JSON.stringify(res))
                    if (res.errMsg === 'requestPayment:ok') {
                        uni.navigateBack()
                    }
                },
                fail: function (err) {
                    console.log('fail:' + JSON.stringify(err))
                }
            } as any)
        })
    })
}

const statusName = (status: string) => {
    switch (status) {
        case '1':
            return '待支付'
        case '2':
            return '已支付'
        case '-1':
            return '已关闭'
        case '3':
            if (status === '3' && details.value.refund_status === 1) {
                return '已退款'
            } else if (status === '3' && details.value.refund_status === 2) {
                return '已完成'
            }
    }
}

// 检查当前时间是否在截止时间之前
const isBeforeCutoff = () => {
    const cutoffTimestamp = getSevenDaysBeforeCutoff(details.value.studyStart_time)
    const nowTimestamp = Date.now()
    return nowTimestamp <= cutoffTimestamp
}

// 退款状态管理
const isRefunding = ref(false)

// 申请退款
const applyRefund = () => {
    // 防止重复点击
    if (isRefunding.value) {
        uni.showToast({
            title: '退款申请处理中，请稍候',
            icon: 'none',
            duration: 1500
        })
        return
    }

    uni.showModal({
        title: '退款确认',
        content: '确定要申请退款吗？',
        confirmText: '确定退款',
        cancelText: '取消',
        success: async (modalRes) => {
            if (modalRes.confirm) {
                await handleRefundRequest()
            }
        }
    })
}

// 处理退款请求
const handleRefundRequest = async () => {
    try {
        isRefunding.value = true

        // 显示加载提示
        uni.showLoading({
            title: '退款申请中...',
            mask: true
        })

        // 调用退款 API
        const response = await courseRefund({ order_id: details.value.order_id })
        const { msg } = response as { msg: string }

        // 隐藏加载提示
        uni.hideLoading()

        // 更新本地订单状态
        if (details.value) {
            details.value.status = '3' // 设置为退款状态
            details.value.refund_status = 1 // 设置退款状态为已退款
        }

        // 显示成功提示
        uni.showToast({
            title: msg || '退款申请成功，请留意您的账户余额',
            icon: 'success',
            duration: 3000,
            mask: true
        })

        // 可选：延迟后刷新页面数据
        setTimeout(() => {
            if (details.value?.order_id) {
                getDetail(details.value.order_id)
            }
        }, 1000)
    } catch (error: any) {
        console.error('退款申请失败:', error)

        // 隐藏加载提示
        uni.hideLoading()

        // 显示错误提示
        const errorMsg = error?.msg || error?.message || '退款申请失败，请稍后重试'
        uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000,
            mask: true
        })
    } finally {
        isRefunding.value = false
    }
}

onLoad((opt) => {
    const { order_id, trade_type } = opt as { order_id: number; trade_type: string }
    console.log(order_id, trade_type)
    getDetail(order_id)
})
</script>
<template>
    <view class="order_detail">
        <ex-header title="订单详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <view class="flex-start-start top_box">
                <image :src="getImageUrl(details.course_index_pic)" mode="aspectFill" />
                <view class="flex1 order_tit">
                    <view class="tit">{{ details.course_title }}</view>
                    <view class="price">¥{{ details.order_money }}</view>
                </view>
            </view>
            <view class="bot_box">
                <view class="items">课程时间：{{ details.studyStart_time }} ~ {{ details.studyEnd_time }}</view>
                <view class="items">培训地点：{{ details.address || '暂无' }}</view>
                <view class="items">课程教练：{{ details.coach_name || '暂无' }}</view>
                <view class="items">报名人姓名：{{ details.member_name || '暂无' }}</view>
                <view class="items">手机号码：{{ details.member_mobile || '暂无' }}</view>
                <view class="items">性别：{{ details.member_sex === 1 ? '男' : '女' }}</view>
                <view class="items">证件号码：{{ details.member_card_id || '暂无' }}</view>
            </view>
        </view>
        <view class="content">
            <view class="titles flex-center-between">
                <text class="title">商品金额</text>
                <text class="title">合计￥{{ details.order_money }}</text>
            </view>
            <view class="flex-center-end growth">
                <text>积分 {{ details.status === '1' ? '+' : '' }}{{ details.growth }}</text>
                <image src="@/static/jifen.png" />
            </view>
        </view>
        <view class="content">
            <view class="flex-center-between">
                <text>订单编号</text>
                <text>{{ details.order_no }}</text>
            </view>
            <view class="flex-center-between">
                <text>订单时间</text>
                <text>{{ details.create_time }}</text>
            </view>
            <view class="flex-center-between">
                <text>订单状态</text>
                <text>{{ statusName(details.status) }}</text>
            </view>
        </view>
        <u-button v-if="details.status === '1'" @tap="pay">支付</u-button>
        <u-button v-if="details.status === '2' && isBeforeCutoff()" @tap="applyRefund" :loading="isRefunding" :disabled="isRefunding">
            {{ isRefunding ? '退款申请中...' : '申请退款' }}
        </u-button>
        <u-button v-if="details.status === '2' && !isBeforeCutoff()" disabled>已完成</u-button>
        <u-button v-if="details.status === '3' || details.refund_status === 2" disabled>已退款</u-button>
        <u-button v-if="details.status === '-1'" disabled>已关闭</u-button>
    </view>
</template>

<style scoped lang="scss">
.order_detail {
    padding: 30rpx;

    .content {
        border: 2rpx solid #535f75;
        background-color: #2f3e5a;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 30rpx;
        font-size: 28rpx;
        color: #ffffff;

        &:last-child {
            margin-bottom: 0;
        }

        .top_box {
            padding-bottom: 20rpx;
            border-bottom: 2rpx solid #535f75;

            image {
                border-radius: 8rpx;
                width: 194rpx;
                height: 132rpx;
                margin-right: 16rpx;
            }

            .order_tit {
                word-break: break-all;
                font-size: 28rpx;
                color: #ffffff;
            }

            .price {
                margin-top: 26rpx;
                font-size: 32rpx;
                color: #fff;
            }
        }

        .bot_box {
            .items {
                margin-top: 20rpx;
                font-size: 28rpx;
                color: #ffffff;
            }
        }

        .titles {
            border-bottom: 2rpx solid #535f75;
            padding-bottom: 20rpx;
            margin-bottom: 20rpx;

            .title {
                font-size: 28rpx;
                color: #ffffff;
            }
        }

        .growth {
            text {
                font-size: 24rpx;
                color: #ffffff;
            }

            image {
                margin-left: 20rpx;
                width: 30rpx;
                height: 30rpx;
            }
        }
    }

    :deep(.u-button) {
        width: 90vw;
        position: fixed;
        bottom: 20rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        border-radius: 90rpx;
        color: #1a2b4a;
    }
}
</style>
