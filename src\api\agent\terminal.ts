import { request } from '@/config/request'

export type dataType = 'myTerminal' | 'subordinateTerminal' | 'teamTerminal'

export interface queryType extends pageType {
    status: string
}

export interface impost extends pageType {
    query_type?: string
    agent_user_nickname?: string
    agent_user_phone?: string
    terminal_brand?: string
    terminal_type?: string
    terminal_version?: string
    terminal_deposit?: string
    terminal_status?: string
    keyword?: string
    [n: string]: string | number | undefined
}

export interface dType {
    id: number
    sn: string
    type: string
    version: string
    deposit: number
    period_of_validity: number
    expiration_time: number
}

// 获取主页数据
export function getData(type: dataType, name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'agentUserTerminal' : 'agentUserLearnTerminal'}/${type}`
    })
}

// 机具划拨统计
export function statistics(name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/statistics`
    })
}

// 划拨、回拨机具列表
export function terminalList(
    data: { limit: number; page: number; search: string; nickname: string; phone: string },
    name: 'pos' | 'learn',
    status: 'g' | 'b'
) {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/${status === 'g' ? 'terminalList' : 'callBackList'}`,
        data
    })
}

// 选择划拨股东姓名
export function getList(data: { limit: number; page: number; search: string }, name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/getList`,
        data
    })
}

// 回拨、划拨机具
export function transferAll(
    data: { implement_sn: string; person_id: number; sync_points: number; synchronized_coupons: number },
    name: 'pos' | 'learn',
    status: 'g' | 'b'
) {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/${status === 'g' ? 'transfer' : 'transCallBack'}`,
        data
    })
}

// 划拨记录、接收记录
export function transferList(data: { tab: number; page: number; limit: number }, name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/transferList`,
        data
    })
}

// 获取划拨参数
export function getTransOption(sn: string, name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'recordsOfTransfers' : 'learnRecordsOfTransfers'}/getTransOption`,
        data: { sn }
    })
}

// 机具列表
export function implementListApi(data: impost, name: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${name === 'pos' ? 'agentUserTerminal' : 'agentUserLearnTerminal'}/terminalList`,
        data
    })
}

// 机具详情
export function implementDetailApi(sn: string, type: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${type === 'pos' ? 'agentUserTerminal' : 'agentUserLearnTerminal'}/terminalDetails`,
        data: { sn }
    })
}

// 股东详情
export function subordinateTerminalListInfo(uid: string, type: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.${type === 'pos' ? 'agentUserTerminal' : 'agentUserLearnTerminal'}/subordinateTerminalListInfo`,
        data: { uid }
    })
}
