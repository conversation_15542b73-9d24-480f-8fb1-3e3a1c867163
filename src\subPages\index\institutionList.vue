<template>
    <view class="classbox">
        <ex-header title="全部机构" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <ex-list ref="orderListRef" :on-form-search="doSearch">
            <template v-slot="{ row }">
                <view class="flex-start-start jllist" @tap="godetail(row.id)">
                    <!-- <image class="img" :src="getImageUrl(row.image_path)" mode=""></image> -->
                    <headerImage
                        :src="getHeaderImage(row.image_path)"
                        width="160rpx"
                        height="160rpx"
                        :is-icon="true"
                        style="border-radius: 50%; overflow: hidden"
                    />
                    <view class="flex1 rightp">
                        <view class="flex-start-start">
                            <text class="flex1 name text-ellipsis-2">{{ row.title }}</text>
                            <view class="class flex-center-start">
                                <image class="icon" src="@/static/jlrz.png" mode=""></image>
                                LNT机构认证
                            </view>
                        </view>
                        <text class="name" style="margin-top: 28rpx">{{ row.status === 1 ? '已认证' : '未认证' }}</text>
                    </view>
                </view>
            </template>
        </ex-list>
    </view>
</template>

<script setup lang="ts">
import { organizationlist } from '@/api/institution/index'
import { getHeaderImage } from '@/utils/common'

const doSearch = (formData: { page: number; limit: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    organizationlist(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}
const godetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/index/institutionDetail?id=' + id
    })
}
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx;

    .jllist {
        margin-bottom: 20rpx;
        padding: 20rpx;
        border-radius: 22rpx;
        background: rgba(242, 242, 242, 0.1961);

        .img {
            width: 132rpx;
            height: 132rpx;
            border-radius: 50%;
        }

        .rightp {
            display: grid;
            margin-left: 36rpx;

            .name {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .class {
                margin-left: 30rpx;
                padding: 7rpx 14rpx;
                border-radius: 14rpx;
                background: #25a3dd;
                font-family: Source Han Sans;
                font-size: 20rpx;
                color: #ffffff;

                .icon {
                    width: 30rpx;
                    height: 30rpx;
                    margin-right: 6rpx;
                }
            }
        }
    }
}
</style>
