import { request } from '@/config/request'

//教练认证
export function authentication(data: any) {
    return request.http({
        url: 'api/shop/coach/authentication',
        method: 'POST',
        data
    })
}

//教练列表
export function coachList(data: any) {
    return request.http({
        url: 'api/shop/coach/list',
        method: 'GET',
        data
    })
}

//教练详情
export function coachDetail(id: string) {
    return request.http({
        url: 'api/shop/coach/detail/' + id,
        method: 'GET'
    })
}

//用户证书(要修改)
export function certList(id: string) {
    return request.http({
        url: 'api/shop/certificate/cert_list?member_id=' + id,
        method: 'GET'
    })
}
