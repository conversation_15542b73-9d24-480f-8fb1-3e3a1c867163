<template>
    <view class="tag_box flex-center-start" :style="{ '--borderColor': borderColor }">
        <view class="border" :style="borderStyle" />
        <text :style="{ color: labelColor }">{{ label }}</text>
        <view class="flex1">
            <slot></slot>
        </view>
    </view>
</template>

<script setup lang="ts">
interface tagsInt {
    label: string
    labelColor?: string
    borderColor?: string
    borderType?: 'round' | 'line'
}

const props = withDefaults(defineProps<tagsInt>(), {
    label: '收益',
    labelColor: '#616161',
    borderColor: '#ff8d1a',
    borderType: 'line'
})

const borderStyle = computed(() => {
    if (props.borderType === 'line') {
        return {
            width: '8rpx',
            height: '28rpx',
            marginRight: '16rpx'
        }
    } else {
        return {
            width: '16rpx',
            height: '16rpx',
            marginRight: '13rpx'
        }
    }
})
</script>

<style scoped lang="scss">
.tag_box {
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
    --borderColor: #ff8d1a;

    .border {
        width: 8rpx;
        height: 28rpx;
        border-radius: 90rpx;
        background-color: var(--borderColor);
    }

    > .flex1 {
        padding-left: 20rpx;
    }
}
</style>
