<script setup lang="ts">
import { about } from '@/api/mine/user'
const richText = ref('')
onMounted(async () => {
    const { data } = (await about()) as { data: { content: string } }
    data.content = data.content
        .replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
        .replace(/<p([^>]*)>/g, function (match, p1) {
            if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                // 如果已有background-color定义，先移除再追加（避免重复）
                return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
            } else if (/style\s*=\s*['"]/.test(p1)) {
                // 有style但无background-color，直接在末尾追加
                return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
            } else {
                // 无style属性，直接添加
                return '<p' + p1 + ' style="background-color: transparent!important;">'
            }
        })
        .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
    richText.value = data.content
})
</script>
<template>
    <view class="about_us">
        <ex-header title="关于我们" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <mp-html :content="richText" />
        </view>
    </view>
</template>

<style scoped lang="scss">
.about_us {
    padding: 30rpx;

    .content {
        padding: 30rpx;
        background-color: #2f3e5a;
        border-radius: 18rpx;
        color: #fff;
        font-size: 28rpx;
        line-height: 1.5;
    }
}
</style>
