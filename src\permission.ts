// import { getAuthorization } from '@/config'

// 白名单
// const whiteList = ['/agentpages/index/index', '/agentpages/mine/index']

export default async function () {
    const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']

    // 用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器
    list.forEach((item) => {
        uni.addInterceptor(item, {
            invoke(e) {
                console.log('e', e)

                // 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
                // const url = e.url.split('?')[0]
                // const type = url.split('/')[1] || ''

                // let data
                // if (getAuthorization()) {
                //     data = JSON.parse(getAuthorization())
                // } else {
                //     data = { userInfo: { is_real: 0 } }
                // }

                // // 判断当前窗口是白名单，如果是则不重定向路由
                // if (type === 'agentpages' && !whiteList.includes(url) && !data.userInfo.is_real) {
                //     uni.showModal({
                //         title: '提示',
                //         content: '请先实名认证',
                //         showCancel: true,
                //         success({ confirm }) {
                //             if (confirm) {
                //                 uni.navigateTo({
                //                     url: '/pages/mine/authentication'
                //                 })
                //             }
                //         }
                //     })
                //     return false
                // }

                return e
            },
            fail(err) {
                // 失败回调拦截
                console.log(err)
            }
        })
    })
}
