import { request } from '@/config/request'

// 小程序首页机构列表（2条数据）
export function organization() {
    return request.http({
        url: 'api/shop/organization/index',
        method: 'GET'
    })
}

// 机构列表
export function organizationlist(data: any) {
    return request.http({
        url: 'api/shop/organization/list',
        method: 'GET',
        data
    })
}

// 机构详情
export function organizationdetail(id: string) {
    return request.http({
        url: 'api/shop/organization/detail/' + id,
        method: 'GET'
    })
}
