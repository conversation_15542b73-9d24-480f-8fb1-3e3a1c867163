/*
 * @description: 监听绑定事件
 * @fileName: useEventListener.ts
 * @author: shenname <<EMAIL>>
 * @date: 2025-03-18 10:01:01
 * @version: v1.0.0
 */
import { unref, onScopeDispose, watch, type WatchStopHandle } from 'vue'

export function useEventListener(...args: any[]): { stop: WatchStopHandle; off: () => void } {
    const element = typeof args[0] === 'string' ? window : args.shift()

    // eslint-disable-next-line prettier/prettier, @typescript-eslint/no-empty-function
    let off = () => { }

    const stop = watch(
        () => unref(element),
        (el) => {
            off()

            if (!el) return

            el.addEventListener(...args)

            off = () => el.removeEventListener(...args)
        },
        { immediate: true }
    )

    onScopeDispose(() => {
        off()
    })

    return {
        stop,
        off
    }
}
