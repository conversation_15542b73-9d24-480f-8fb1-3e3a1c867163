/* eslint-disable prettier/prettier */

declare global {
    type useType = 'client' | 'agent'

    /**
     * 公共的路径
     * <AUTHOR> <<EMAIL>>
     * @license MIT
     * @param { string } fileName 文件名称
     * @param { string } filePath 文件路径
     * @example
     * fileName: images.png
     * filePath: images
     */
    interface imgUrlInt {
        fileName: string
        filePath?: string
    }

    interface pageType {
        page: number
        limit: number
        total?: number
    }

}

export { }
