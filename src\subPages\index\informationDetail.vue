<template>
    <u-navbar
        title="资讯"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <image class="img" :src="getImageUrl(data.pic_path)" mode=""></image>
        <text class="title">{{ data.title }}</text>
        <text class="time">{{ data.create_time }}</text>
        <mp-html :content="data.content" />

        <view class="buts">
            <view class="part">
                <image class="icon" src="@/static/sc.png" mode=""></image>
                收藏
            </view>
            <view class="part">
                <image class="icon" src="@/static/zf.png" mode=""></image>
                分享
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { articleDetail } from '@/api/index/index'
import { getImageUrl } from '@/utils/common'

const data = ref({})

onLoad(async (e) => {
    await articleDetail(e.id).then((res: any) => {
        console.log(res)
        res.data.content = res.data.content
            .replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
            .replace(/<p([^>]*)>/g, function (match, p1) {
                if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                    // 如果已有background-color定义，先移除再追加（避免重复）
                    return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
                } else if (/style\s*=\s*['"]/.test(p1)) {
                    // 有style但无background-color，直接在末尾追加
                    return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
                } else {
                    // 无style属性，直接添加
                    return '<p' + p1 + ' style="background-color: transparent!important;">'
                }
            })
            .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
        data.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    max-height: calc(98vh - 174rpx);
    overflow-y: auto;
    background: #0e2942;
    display: grid;
    align-content: flex-start;
    box-sizing: border-box;
    padding: 40rpx 60rpx;
    justify-items: center;

    .img {
        width: 100%;
        height: 432rpx;
    }

    .title {
        margin-top: 26rpx;
        font-family: Source Han Sans;
        font-size: 36rpx;
        font-weight: bold;
        line-height: normal;
        letter-spacing: 2rpx;
        color: #ffffff;
    }

    .time {
        margin-top: 20rpx;
        font-family: Source Han Sans;
        font-size: 28rpx;
        font-weight: bold;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #ffffff;
    }

    .buts {
        width: 100%;
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 28rpx;

        .part {
            display: grid;
            justify-items: center;
            font-family: Source Han Sans;
            font-size: 20rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;

            .icon {
                width: 40rpx;
                height: 40rpx;
                margin-bottom: 10rpx;
            }
        }
    }
}
</style>
