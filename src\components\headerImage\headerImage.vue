<script setup lang="ts">
import { toRefs, computed, type CSSProperties } from 'vue'

const props = withDefaults(
    defineProps<{
        src: string
        mode?: string
        width?: string
        height?: string
        isIcon?: boolean
        imgs?: string[]
    }>(),
    {
        src: '',
        mode: 'aspectFill',
        width: '80rpx',
        height: '80rpx',
        type: 'list',
        imgs: () => []
    }
)

const { src, mode, width, height, isIcon } = toRefs(props)

const imgStyle = computed(() => {
    const obj: CSSProperties = { width: width.value, height: height.value }

    if (!isIcon.value) {
        return Object.assign(obj, {
            padding: '14rpx',
            backgroundColor: '#dedede',
            borderRadius: '4px'
        })
    }

    return obj
})

const handleImageError = () => (src.value = `@/static/default-${isIcon.value ? 'icon' : 'list'}.png`)
</script>

<template>
    <view class="ex-images" :style="imgStyle">
        <image :src="src" @error="handleImageError" :mode="mode" />
    </view>
</template>

<style scoped lang="scss">
.ex-iamges {
    image {
        width: 100%;
        height: 100%;
    }
}
</style>
