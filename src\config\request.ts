import useUserStore from '../store/user'

interface ResponseOptions {
    url: string
    headers?: { [key: string]: string }
    method?: 'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT'
    data?: { [key: string]: any }
    isSinglePost?: boolean
}

export const request = {
    isLock: false,
    http({ url = '', headers = {}, data = {}, method = 'POST', isSinglePost = false }: ResponseOptions) {
        const _this = this
        const userStore = useUserStore()

        return new Promise(function (resolve, reject) {
            if (isSinglePost && _this.isLock) {
                reject({ message: '加载中' })
            }
            _this.isLock = true

            // #ifdef MP-WEIXIN
            url = import.meta.env.VITE_APP_BASE_URL + url
            // #endif
            // #ifdef H5
            url = import.meta.env.VITE_APP_BASE_PRE + url
            // #endif

            const header = Object.assign({ 'content-type': 'application/json', Authorization: '' }, headers)

            if (userStore.token) {
                header['token'] = userStore.token
            }
            uni.showLoading({ title: '加载中', mask: true })

            uni.request({
                url,
                header,
                method,
                data,
                success(res) {
                    const data = res.data as { code: number; data: object; msg: string }
                    switch (data.code) {
                        case 401:
                            uni.showToast({ title: data.msg, icon: 'none' })
                            setTimeout(() => uni.navigateTo({ url: '/pages/login/login' }), 1000)
                            break
                        case 1:
                            resolve(res.data)
                            break
                        default:
                            uni.showToast({ title: data.msg, icon: 'none', mask: true })
                            reject(res.data)
                            break
                    }
                    setTimeout(() => {
                        uni.hideLoading()
                    }, 500)
                },
                fail(err) {
                    console.log('err', err)
                    uni.showToast({ title: '网络错误', icon: 'none' })
                    reject(err)
                    setTimeout(() => {
                        uni.hideLoading()
                    }, 500)
                },
                complete() {
                    _this.isLock = false
                }
            })
        })
    }
}
