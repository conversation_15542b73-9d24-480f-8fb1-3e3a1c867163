<template>
    <view class="list-title">
        <view class="star">
            <mp-html :content="svgContent" />
        </view>
    </view>
</template>

<script setup lang="ts">
const svgContent = `
<svg viewBox="0 0 50 50" xmlns="http://www.w3.org/2000/svg" style="width:100%;height:100%;"><path d="M25,3 C25.6,3 26.1,3.3 26.3,3.9 L31.1,16.6 C31.3,17.3 32,17.9 32.7,17.9 L46.2,17.9 C47.8,17.9 48.5,19.9 47.2,20.9 L36.7,28.5 C36.1,28.9 35.8,29.7 36,30.4 L40.8,43.1 C41.3,44.6 39.6,45.9 38.3,44.9 L27.8,37.3 C27.2,36.9 26.4,36.9 25.8,37.3 L15.3,44.9 C14,45.9 12.3,44.6 12.8,43.1 L17.6,30.4 C17.8,29.7 17.5,28.9 16.9,28.5 L6.4,20.9 C5.1,19.9 5.8,17.9 7.4,17.9 L20.9,17.9 C21.6,17.9 22.3,17.3 22.5,16.6 L27.3,3.9 C27.5,3.3 28,3 28.6,3 L25,3 Z" fill="#FFD700"/></svg>
`;
</script>

<style lang="scss" scoped>
.list-title {
    .star {
        position: relative;
        display: inline-block;
        width: 60rpx;
        height: 60rpx;
        margin-right: 10rpx;
    }
}
</style>