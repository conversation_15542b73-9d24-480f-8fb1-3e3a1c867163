import { request } from '@/config/request'

const userPrefix = '/api/t.school'
const agentPrefix = '/api/t.school'

export function list(data: pageType, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/list`,
        data
    })
}

export function info(id: number, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/details`,
        data: { id }
    })
}
