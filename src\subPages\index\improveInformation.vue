<template>
    <ex-header title="完善信息" background-color="#1a2b4a" text-color="#fff" mode="dark" />
    <view class="classbox">
        <view class="head">
            <image class="icon" src="@/static/txgrxx.png" mode=""></image>
            <text class="title">填写个人信息</text>
        </view>
        <u-form
            label-position="left"
            :model="formData"
            :rules="rules"
            ref="uFormRef"
            :label-style="{ fontSize: '28rpx', color: '#FFFFFF', marginLeft: '4rpx' }"
            label-width="140rpx"
        >
            <u-form-item label="姓名" prop="name" :border-bottom="true" required>
                <u-input v-model="formData.name" clearable border="none" color="#ffffff" />
            </u-form-item>
            <u-form-item label="性别" border-bottom prop="sex" required>
                <view class="sex">
                    <u-subsection :list="sexList" :current="formData.sex" @change="sexChange" />
                </view>
            </u-form-item>
            <u-form-item label="手机号" prop="number" :border-bottom="true" required>
                <u-input
                    :custom-style="{ padding: '20rpx 20rpx', background: 'rgba(255, 255, 255, 0.1216)' }"
                    :model-value="userStore.userInfo.mobile"
                    border="none"
                    color="#ffffff"
                    readonly
                    clearable
                />
            </u-form-item>
            <u-form-item label=" 证件号码" prop="card_id" :border-bottom="true" required>
                <u-input
                    :custom-style="{ padding: '20rpx 20rpx', background: 'rgba(255, 255, 255, 0.1216)' }"
                    v-model="formData.card_id"
                    border="none"
                    color="#ffffff"
                    clearable
                />
            </u-form-item>
            <u-form-item label="一寸免冠照" border-bottom prop="photo" required>
                <view class="grid">
                    <u-upload
                        accept="image"
                        :file-list="fileList"
                        @afterRead="afterRead"
                        @delete="deletePic"
                        multiple
                        :max-count="1"
                        width="60"
                        height="60"
                    />
                </view>
            </u-form-item>
        </u-form>
        <u-button @tap="submit">保存</u-button>
    </view>
</template>

<script setup lang="ts">
import useUserStore from '@/store/user'
import { uploadFile } from '@/api/common/common'
import { getImageUrl } from '@/utils/common'
import { certification } from '@/api/mine/user'
const userStore = useUserStore()
const formData = reactive({
    name: '',
    sex: 1,
    card_id: '',
    photo: ''
})
const sexList = ref([
    { name: '未知', value: 0 },
    { name: '男', value: 1 },
    { name: '女', value: 2 }
])
const sexChange = (value: number) => (formData.sex = sexList.value[value].value)

// 校验规则
const rules = {
    name: [
        {
            type: 'string',
            required: true,
            message: '请输入姓名',
            trigger: ['blur', 'change']
        }
    ],
    sex: [
        {
            type: 'number',
            required: true,
            message: '请选择性别',
            trigger: ['blur', 'change']
        }
    ],
    card_id: [
        {
            type: 'string',
            required: true,
            message: '请输入身份证号码',
            trigger: ['change', 'blur']
        },
        {
            validator: (_rule: any, value: any, _callback: any) => {
                return uni.$u.test.idCard(value)
            },
            message: '身份证号码不正确',
            trigger: ['change', 'blur']
        }
    ],
    photo: [
        {
            type: 'string',
            required: true,
            message: '请上传一寸免冠照',
            trigger: ['change', 'blur']
        }
    ]
}

const fileList = ref<any[]>([])
const deletePic = (event: { index: number }) => {
    formData.photo = ''
    fileList.value.splice(event.index, 1)
}
const afterRead = async (event: any) => {
    const lists: any = [].concat(event.file)
    let fileListLen = fileList.value.length
    lists.map((item: any) => {
        fileList.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        })
    })
    for (let i = 0; i < lists.length; i++) {
        const result: any = await uploadFile(lists[i].url)
        let item = fileList.value[fileListLen]
        formData.photo = result.data.url
        fileList.value.splice(
            fileListLen,
            1,
            Object.assign(item, {
                status: 'success',
                message: '',
                url: getImageUrl(result.data.url),
                urls: result.data.url
            })
        )
        fileListLen++
    }
    console.log(123123123, fileList.value)
}

// 表单引用
const uFormRef = ref()
function submit() {
    uFormRef.value
        .validate()
        .then((valid: boolean) => {
            if (valid) {
                uni.showToast({ title: '校验成功', icon: 'none', mask: true })
                certification(formData).then(async () => {
                    uni.showToast({ title: '提交成功', icon: 'none', mask: true })
                    await userStore.getUserInfo()
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                })
            } else {
                // uni.showToast({ title: '校验失败', icon: 'none', mask: true })
            }
        })
        .catch(() => {
            // 处理验证错误
            // uni.showToast({ title: '校验失败1', icon: 'none', mask: true })
        })
}
onMounted(() => {
    const { name, sex, card_id, photo } = userStore.userInfo
    formData.name = name
    formData.sex = sex
    formData.card_id = card_id
    formData.photo = photo
    fileList.value = photo ? [{ url: getImageUrl(photo), urls: photo, status: 'success', type: 'image' }] : []
})
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx 50rpx;

    .head {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;

        .icon {
            width: 60rpx;
            height: 60rpx;
        }

        .title {
            margin-left: 36rpx;
            font-family: Source Han Sans;
            font-size: 40rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }
    }

    :deep(.u-form-item__body__left__content__label) {
        white-space: nowrap;
    }

    .sm {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
    }

    .sex {
        width: 50%;
    }

    :deep(.u-form-item__body__right__content__slot) {
        justify-content: flex-end;
    }

    :deep(.u-button) {
        width: 90vw;
        position: fixed;
        bottom: 30rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        border-radius: 90rpx;
        color: #1a2b4a;
    }
}
</style>
