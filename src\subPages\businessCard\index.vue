<script setup lang="ts">
import { uploadFile } from '@/api/common/common'
import { updateUser } from '@/api/mine/user'
import useUserStore from '@/store/user'
import { getHeaderImage } from '@/utils/common'
const userStore = useUserStore()
const form = ref()
const formData = reactive({
    nickname: userStore.userInfo.nickname,
    headimg: userStore.userInfo.headimg,
    sex: userStore.userInfo.sex,
    introduction: userStore.userInfo.introduction
})
const showSex = ref(false)
const actions = reactive([
    { name: '男', value: 1 },
    { name: '女', value: 2 }
])

const sexSelect = (item: any) => {
    formData.sex = item.value
    showSex.value = false
}

const uploadAvatar = () => {
    uni.chooseImage({
        count: 1,
        success: async (res) => {
            console.log(123123, res)
            const { data } = (await uploadFile(res.tempFilePaths[0])) as { data: { url: string } }
            formData.headimg = data.url
        }
    })
}

const rules = reactive({
    nickname: {
        type: 'string',
        required: true,
        message: '请填写姓名',
        trigger: ['blur', 'change']
    }
})

const submit = uni.$util.throttle(() => {
    form.value
        .validate()
        .then((valid: boolean) => {
            if (valid) {
                updateUser({ data: formData, handle_type: 2 }).then(() => {
                    uni.showToast({ title: '修改成功', icon: 'none', mask: true })
                    userStore.getUserInfo().then((res) => {
                        console.log(123123, res)
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                })
            } else {
                // uni.showToast({ title: '校验失败', icon: 'none', mask: true })
            }
        })
        .catch(() => {
            // 处理验证错误
            // uni.showToast({ title: '校验失败', icon: 'none', mask: true })
        })
})
</script>
<template>
    <view class="business_card">
        <ex-header title="我的名片" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <u-form ref="form" :model="formData" :rules="rules" :label-style="{ fontSize: '30rpx', color: '#fff' }" label-width="140rpx">
                <u-form-item label="姓名" border-bottom prop="nickname" required>
                    <u-input
                        v-model="formData.nickname"
                        border="none"
                        clearable
                        color="#fff"
                        input-align="right"
                        placeholder="请输入姓名"
                        maxlength="10"
                    />
                </u-form-item>
                <u-form-item label="头像" border-bottom prop="headimg" required>
                    <view class="flex-center-end items_content" @click="uploadAvatar">
                        <image class="image" :src="getHeaderImage(formData.headimg)" mode="aspectFill" />
                        <u-icon class="icon" name="arrow-right" size="14" color="#a6a6a6" />
                    </view>
                </u-form-item>
                <u-form-item label="个人介绍" border-bottom>
                    <u-textarea
                        v-model="formData.introduction"
                        border="none"
                        placeholder="请输入个人介绍"
                        maxlength="100"
                        placeholder-style="color: #aaaaaa;"
                    />
                </u-form-item>
                <u-form-item label="性别" @click="showSex = true">
                    <u-input
                        :model-value="formData.sex === 1 ? '男' : formData.sex === 2 ? '女' : '保密'"
                        readonly
                        placeholder="请选择性别"
                        border="none"
                        input-align="right"
                        color="#fff"
                    />
                    <template #right>
                        <u-icon name="arrow-right" color="#fff" />
                    </template>
                </u-form-item>
            </u-form>
        </view>
        <u-button class="button" @click="submit">保存</u-button>
        <u-action-sheet :show="showSex" :actions="actions" title="请选择性别" @close="showSex = false" @select="sexSelect"></u-action-sheet>
    </view>
</template>

<style scoped lang="scss">
.business_card {
    padding: 30rpx;

    .content {
        padding: 30rpx 40rpx;
        background-color: #233351;
        border-radius: 18rpx;

        :deep(.u-form-item__body__right__message) {
            text-align: right;
        }

        .items_content {
            width: 100%;

            image {
                width: 100rpx;
                height: 100rpx;
                border-radius: 50%;
                margin-right: 20rpx;
            }
        }

        :deep(.u-textarea) {
            background-color: #2c3b58;
        }

        :deep(.u-textarea__field) {
            color: #fff;
        }
    }

    :deep(.u-button) {
        position: fixed;
        bottom: 30rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        border-radius: 90rpx;
        width: 90vw;
    }
}
</style>
