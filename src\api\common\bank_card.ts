import { request } from '@/config/request'

export interface updateType {
    card_number: string
    real_name: string
    category_id: number | string | undefined
    phone: string
    code: string
    bank_name: string
    bank_logo: string
}

export function update(data: updateType, type: useType) {
    return request.http({
        url: `/api/${type === 'client' ? 'y.clientBankCard/addOrUpdate' : 'd.AgentBankCard/myBillingCard'}`,
        data
    })
}
