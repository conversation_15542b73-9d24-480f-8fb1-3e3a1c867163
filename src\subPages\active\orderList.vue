<script setup lang="ts">
import { getImageUrl } from '@/utils/common'
import { mySign } from '@/api/active/index'
import { reactive, ref } from 'vue'
const navto = (url: string, params = {}) => uni.$util.goToPage({ url, params })
const orderListRef = ref()
const currentIndex = ref(0)
const list = ref<{ name: string; value: string }[]>([
    { name: '全部', value: '' },
    { name: '已报名', value: '4' },
    { name: '已结束', value: '5' }
])
const query = reactive({ activity_status: '' })

const compQueryData = (index: number) => {
    currentIndex.value = index
    query.activity_status = list.value[index].value
    orderListRef.value.refreshFn()
}

const doSearch = (formData: { page: number; limit: number; activity_status: string }, onSuccess: Function) => {
    const submitData = { ...formData }
    mySign(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}
</script>
<template>
    <view class="course">
        <ex-header title="活动订单" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <u-subsection :list="list" :current="currentIndex" bg-color="#1a2b4a" inactive-color="#fff" mode="subsection" @change="compQueryData" />
        <view class="content">
            <ex-list ref="orderListRef" :options="query" :on-form-search="doSearch">
                <template v-slot="{ row }">
                    <view class="items" @tap="navto('subPages/active/classorderDetail', { id: row.id })">
                        <view class="fm_box">
                            <image :src="getImageUrl(row.pic_path)" mode="aspectFill" />
                        </view>
                        <view class="text-ellipsis-2 title">{{ row.title }}</view>
                        <view class="date">活动时间：{{ row.activity_start_time }} ~ {{ row.activity_end_time }}</view>
                    </view>
                </template>
            </ex-list>
        </view>
    </view>
</template>

<style scoped lang="scss">
.course {
    :deep(.u-subsection__item) {
        border: none !important;
        border-radius: unset !important;
    }

    :deep(.u-subsection__bar--first) {
        border-top-left-radius: unset !important;
        border-bottom-left-radius: unset !important;
    }

    :deep(.u-subsection__bar--last) {
        border-top-right-radius: unset !important;
        border-bottom-right-radius: unset !important;
    }

    .content {
        padding: 30rpx;

        .items {
            border-radius: 18rpx;
            padding: 30rpx;

            .time {
                font-size: 32rpx;
                color: #ffffff;
            }

            .fm_box {
                position: relative;

                image {
                    border-radius: 18rpx;
                    width: 100%;
                    height: 340rpx;
                    margin: 20rpx 0 10rpx;
                }

                text {
                    padding: 6rpx 12rpx;
                    font-size: 28rpx;
                    color: #ffffff;
                    background-color: #25a3dd;
                    border-top-right-radius: 18rpx;
                    border-bottom-right-radius: 18rpx;
                    position: absolute;
                    top: 60rpx;
                    left: 0;
                }
            }

            .title {
                word-break: break-all;
                margin-bottom: 16rpx;
                font-size: 32rpx;
                color: #ffffff;
            }

            .date {
                font-size: 24rpx;
                color: #ffffff;
            }
        }
    }
}
</style>
