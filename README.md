## 项目介绍

[uniapp-vue-vite-ts](https://gitee.com/lxxlalala/uniapp-vue-vite-ts) 是基于 Vue3 + Vite4+ TypeScript +  uview-plus V3 + Pinia 等最新主流技术栈构建的uniapp前端模板。

| 环境                 | 名称版本                                                     |
| -------------------- | :----------------------------------------------------------- |
| **开发工具**         | VSCode                                                       |
| **运行环境**         | Node 16+                                                     |
| **VSCode插件(必装)** | 1. `Vue Language Features (Volar) ` <br/>2. `TypeScript Vue Plugin (Volar) `  <br/>3. 禁用 Vetur <br/>4.ESLint <br/>5.Prettier  - Code formatter |



## 项目启动

```bash
# 安装 pnpm
npm install pnpm -g

# 安装依赖
pnpm install

# 启动运行
pnpm run dev:h5 (或者查看package.json运行对应平台命令)
```



## 项目文档

- [ESLint+Prettier+<PERSON><PERSON> 约束和统一前端代码规范](https://blog.csdn.net/qq_51091386/article/details/132099829)
