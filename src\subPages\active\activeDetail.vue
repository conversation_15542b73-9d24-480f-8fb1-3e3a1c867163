<template>
	<u-navbar title="活动详情" bg-color="#0E2942" left-icon-color="#FFFFFF" auto-back
		:title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"></u-navbar>
	<view class="classbox">
		<view class="piccard">
			<view class="leven">{{ Data.category_id_name }}</view>
			<image style="width: 100%; height: 348rpx" :src="getImageUrl(Data.pic_path)" mode=""></image>
		</view>
		<view class="headpart">
			<text class="title">{{ Data.title }}</text>
			<text class="status">状态：{{ Data.statusArr?.name }}</text>
			<text class="status">活动类型：{{ Data.is_price===1?'付费' :'免费' }}</text>
			<text class="status" v-if="Data.is_price===1">金额：{{ Data.price }}</text>
			<text class="time">报名时间：{{ Data.sign_start_time }}~{{ Data.sign_end_time }}</text>
			<text class="time">活动地点：{{ Data.address }}</text>
			<text class="time">时间：{{ Data.activity_start_time }}~{{ Data.activity_end_time }}</text>
		</view>
		<text class="mok">活动介绍</text>
		<mp-html :content="Data.introduce" />

		<text class="mok">参与人员{{ Data.sign_num }}/{{ Data.num }}</text>
		<view class="bottom">
			<view class="check">
				<u-checkbox :custom-style="{ marginBottom: '16rpx' }" active-color="#1A2B4A" inactive-color="#1A2B4A"
					name="agree" used-alone v-model:checked="aloneChecked"></u-checkbox>
				<view class="txttk">
					同意<view class="yhxy" @click="goxytk(1)">
						用户协议
					</view>与<view class="ystk" @click="goxytk(2)">
						隐私条款
					</view>
				</view>
			</view>
			<view class="butt">
				<button style="width: 10%" open-type="share" @tap.stop="handleShareClick">
					<image style="width: 40rpx; height: 40rpx" src="@/static/share.png" mode=""></image>
				</button>
				<view class="but" @click="gocj">立即参加</view>
			</view>
		</view>
	</view>
	<u-modal :show="showtc" title="提示" show-cancel-button="true" content="确认参加" @confirm="confirm"
		@cancel="cancel"></u-modal>
</template>

<script setup lang="ts">
	import { ref } from 'vue'
	import { activityDetail } from '@/api/active/index'
	import { getImageUrl } from '@/utils/common'
	import useUserStore from '@/store/user'
	const userStore = useUserStore()

	const showtc = ref(false)
	const confirm = () => {
		showtc.value = false
		if(userStore.userInfo.name) {
			uni.navigateTo({
				url: '/subPages/active/signUp?id=' + Data.value.id
			})
		} else {
			uni.showModal({
				title: '请登录后操作',
				confirmText:'去登陆',
				success: (res) => {
					if(res.confirm) {
						uni.navigateTo({
							url:'/pages/login/login'
						})
					}
				}
			})
		}
	}

	const Data = ref({})

	const aloneChecked = ref(false)

	const gocj = () => {
		if (aloneChecked.value === false) {
			uni.showToast({
				icon: 'fail',
				title: '请勾选用户协议与隐私条款'
			})
		} else {
			showtc.value = true
		}
	}

	const cancel = () => {
		showtc.value = false
	}

	// 处理分享按钮点击
	const handleShareClick = (event : Event) => {
		event.stopPropagation() // 阻止事件冒泡
	}
	
	const goxytk = (type: number) => {
		uni.navigateTo({
			url: '/subPages/active/agreementTerms?type='+type
		})
	}

	onLoad(async (e : any) => {
		await activityDetail(e.id).then((res : any) => {
			Data.value = res.data
			Data.value.introduce = Data.value.introduce
				.replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
				.replace(/<p([^>]*)>/g, function (match, p1) {
					if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
						// 如果已有background-color定义，先移除再追加（避免重复）
						return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
					} else if (/style\s*=\s*['"]/.test(p1)) {
						// 有style但无background-color，直接在末尾追加
						return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
					} else {
						// 无style属性，直接添加
						return '<p' + p1 + ' style="background-color: transparent!important;">'
					}
				})
				.replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
		})
	})
</script>

<style scoped lang="scss">
	.classbox {
		margin-top: 174rpx;
		width: 100vw;
		display: grid;
		align-content: flex-start;
		padding: 24rpx 32rpx 160rpx 32rpx;
		box-sizing: border-box;

		.piccard {
			.leven {
				white-space: nowrap;
				position: absolute;
				top: 228rpx;
				padding: 6rpx 20rpx;
				box-sizing: border-box;
				background-color: #1a2b4a;
				font-family: Source Han Sans;
				font-size: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
				border-radius: 0 30rpx 30rpx 0;
			}
		}

		.headpart {
			margin-top: 18rpx;
			display: grid;
			align-items: center;
			justify-items: center;

			.title {
				font-family: Source Han Sans;
				font-size: 32rpx;
				font-weight: normal;
				line-height: normal;
				letter-spacing: normal;
				color: #ffffff;
			}

			.status {
				margin-top: 8rpx;
				font-family: Source Han Sans;
				font-size: 24rpx;
				font-weight: normal;
				line-height: normal;
				letter-spacing: 2rpx;
				color: #ffffff;
			}

			.time {
				margin-top: 8rpx;
				font-family: Source Han Sans;
				font-size: 24rpx;
				font-weight: normal;
				line-height: normal;
				letter-spacing: 2rpx;
				color: #ffffff;
			}
		}

		.mok {
			margin: 36rpx 0;
			padding-left: 30rpx;
			padding-right: 30rpx;
			width: fit-content;
			height: 46rpx;
			font-family: Source Han Sans;
			font-size: 26rpx;
			font-weight: normal;
			line-height: normal;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			letter-spacing: normal;
			color: #1a2b4a;
			border-radius: 10rpx;
			background-color: #ffffff;
		}

		.txthdjs {
			font-size: 24rpx;
			color: #ffffff;
		}

		.txt {
			margin-left: 30rpx;
			margin-top: 28rpx;
			font-family: Source Han Sans;
			font-size: 24rpx;
			font-weight: normal;
			line-height: normal;
			letter-spacing: 2rpx;
			color: #ffffff;
		}

		.pic {
			margin-left: 30rpx;
			margin-top: 16rpx;
			width: 80%;
			height: 342rpx;
		}

		.bottom {
			position: fixed;
			bottom: 40rpx;
			display: grid;

			.check {
				display: flex;
				align-items: baseline;

				:deep(.u-checkbox__icon-wrap) {
					background-color: #1a2b4a !important;
					border: 1rpx solid #ffffff;
					border-color: #ffffff !important;
					width: 28rpx !important;
					height: 28rpx !important;
				}

				:deep(.u-checkbox__label-wrap) {
					text {
						font-size: 28rpx !important;
						color: #ffffff !important;
					}
				}

				.txttk {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #ffffff;
					.yhxy {
						font-size: 28rpx;
						color: #ffffff;
						text-decoration: underline;
					}
					.ystk {
						font-size: 28rpx;
						color: #ffffff;
						text-decoration: underline;
					}
				}
			}


			.butt {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.but {
					margin-left: 32rpx;
					width: 602rpx;
					height: 78rpx;
					font-family: Source Han Sans;
					font-size: 26rpx;
					font-weight: normal;
					line-height: normal;
					text-align: center;
					display: flex;
					align-items: center;
					justify-content: center;
					letter-spacing: normal;
					color: #1a2b4a;
					border-radius: 40rpx;
					background-color: #ffffff;
				}
			}
		}
	}
</style>