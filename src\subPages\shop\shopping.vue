<template>
    <ex-header title="购物车" background-color="#1a2b4a" text-color="#fff" :on-goback="goBack" mode="dark" />
    <view class="classbox">
        <view class="search">
            <u-search
                placeholder=""
                v-model="keyword"
                bg-color="#f7f8fa"
                :action-style="{ fontFamily: 'Source Han Sans', fontSize: '28rpx', color: '#FFFFFF' }"
            ></u-search>
        </view>
        <view class="shoplist">
            <view class="shopone" v-for="(item, index) in shoplist" :key="index">
                <view class="card">
                    <view style="min-width: 50rpx">
                        <u-checkbox
                            v-model:checked="item.checked"
                            :name="item.id"
                            :key="index"
                            shape="square"
                            used-alone
                            active-color="#FFFFFF"
                            inactive-color="#FFFFFF"
                        ></u-checkbox>
                    </view>
                    <image
                        :src="getImageUrl(item.goods?.goods_cover_thumb_small)"
                        mode="aspectFill"
                        style="width: 190rpx; height: 190rpx; margin-left: 20rpx"
                    ></image>
                    <view style="display: grid; width: 63%">
                        <view class="rightp">
                            <view class="name">
                                {{ item.goods.goods_name }}
                            </view>
                            <view class="ms">描述信息</view>
                            <view
                                style="width: 100%; display: flex; align-items: center; margin-top: 10rpx"
                                :style="item.goodsSku.sku_spec_format ? 'justify-content: space-between;' : 'justify-content: flex-end;'"
                            >
                                <view class="bqlist" v-if="item.goodsSku.sku_spec_format">
                                    <view class="bq" v-for="(ele, ind) in item.goodsSku.sku_spec_format.split(',')" :key="ind">
                                        {{ ele }}
                                    </view>
                                </view>
                                <u-number-box v-model="item.num" :name="index" button-size="26"></u-number-box>
                            </view>
                            <view class="pricepp">
                                <text class="price">￥{{ item.goodsSku.price }}</text>
                                <text class="yprice">￥{{ item.goodsSku.market_price }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="buts">
                    <view class="but" @click="deleteone(item.id)">删除</view>
                    <view class="but">购买</view>
                </view>
            </view>
        </view>
        <view class="cart-cal">
            <u-checkbox
                v-model:checked="checkboxValue"
                name="checkAll"
                shape="square"
                label="全选"
                label-color="#FFFFFF"
                used-alone
                @change="checkboxChange"
                active-color="#ccc8c8"
                inactive-color="#ccc8c8"
            ></u-checkbox>
            <view class="cart-cal-right">
                <view>
                    <text class="hj">合计：</text>
                    <text style="font-size: 28rpx; color: #25a3dd">￥{{ 80.01 }}</text>
                </view>
                <view style="height: 100%">
                    <view class="sumit" @click="settlement">提交订单</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { cartList, deleteCart } from '@/api/shop/index'
import { getImageUrl } from '@/utils/common'
const keyword = ref('')
const shoplist = ref([])
const checkboxValue = ref(false)
function checkboxChange(e: boolean) {
    shoplist.value.forEach((cart) => {
        cart.checked = e // .includes('checkAll')
    })
}

const deleteone = async (id: number) => {
    await deleteCart({ ids: id }).then((res: any) => {
        if (res.code === 1) {
            uni.showToast({
                icon: 'success',
                title: res.msg,
                duration: 1000,
                success: async () => {
                    await cartList().then((res: any) => {
                        shoplist.value = res.data
                    })
                }
            })
        }
    })
}

const settlement = () => {
    console.log(shoplist.value)
}

onShow(async () => {
    await cartList().then((res: any) => {
        shoplist.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    .search {
        width: 100%;
        padding: 24rpx 32rpx;
    }

    .shoplist {
        width: 100%;
        display: grid;

        .shopone {
            margin-top: 6rpx;
            width: 100%;
            background-color: #47546d;
            display: grid;

            .card {
                width: 100%;
                background-color: #47546d;
                display: flex;
                align-items: center;
                padding: 36rpx 26rpx 0 26rpx;

                :deep(.u-checkbox__icon-wrap) {
                    background-color: #0e294200 !important;
                }

                .rightp {
                    margin-left: 20rpx;
                    display: grid;

                    .name {
                        font-family: Source Han Sans;
                        font-size: 24rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #ffffff;
                    }

                    .ms {
                        margin-top: 14rpx;
                        font-family: Source Han Sans;
                        font-size: 24rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #d7d7d7;
                    }

                    .bqlist {
                        display: flex;
                        align-items: center;

                        .bq {
                            margin-right: 20rpx;
                            border-radius: 22rpx;
                            background: #ffffff;
                            box-sizing: border-box;
                            border: 2rpx solid #3bace0;
                            padding: 2rpx 16rpx;
                            font-family: Source Han Sans;
                            font-size: 24rpx;
                            font-weight: normal;
                            line-height: normal;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            letter-spacing: normal;
                            color: #25a3dd;
                        }
                    }

                    :deep(.u-number-box__input) {
                        margin: 0 !important;
                    }

                    :deep(.u-number-box__minus) {
                        border-radius: 10rpx 0 0 10rpx !important;
                    }

                    :deep(.u-number-box__plus) {
                        border-radius: 0 10rpx 10rpx 0 !important;
                    }

                    .pricepp {
                        margin-top: 12rpx;
                        display: flex;

                        .price {
                            font-family: Source Han Sans;
                            font-size: 28rpx;
                            font-weight: normal;
                            line-height: normal;
                            letter-spacing: normal;
                            color: #f6a538;
                        }

                        .yprice {
                            margin-left: 10rpx;
                            font-family: Source Han Sans;
                            font-size: 20rpx;
                            font-weight: normal;
                            line-height: normal;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            letter-spacing: normal;
                            color: #ffffff;
                            text-decoration: line-through;
                        }
                    }
                }
            }
        }

        .buts {
            width: 100%;
            text-align: end;
            display: flex;
            align-items: center;
            gap: 12rpx;
            justify-content: flex-end;
            margin-bottom: 10rpx;
            padding-right: 26rpx;

            .but {
                width: 70rpx;
                height: 36rpx;
                font-family: Source Han Sans;
                font-size: 20rpx;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: normal;
                color: #ffffff;
                background-color: #25a3dd;
                border-radius: 10rpx;
            }
        }
    }

    .cart-cal {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 108rpx;
        box-sizing: border-box;
        background: #1a2b4a;
        position: fixed;
        width: 100%;
        bottom: 0;
        left: 0;

        :deep(.u-checkbox__icon-wrap) {
            background-color: #0e294200 !important;
        }

        .cart-cal-right {
            display: flex;
            align-items: center;
            gap: 20rpx;
            height: 100%;

            .hj {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }

        .sumit {
            height: 100%;
            width: 198rpx;
            font-family: Source Han Sans;
            font-size: 26rpx;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: normal;
            color: #1a2b4a;
            background-color: #ffffff;
        }
    }
}
</style>
