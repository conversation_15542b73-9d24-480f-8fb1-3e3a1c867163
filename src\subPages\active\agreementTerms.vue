<template>
    <view class="classbox">
        <ex-header title="协议条款" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <rich-text :nodes="data"></rich-text>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { contentProtocol } from '@/api/active/index'
import { getImageUrl } from '@/utils/common'
import { onLoad } from '@dcloudio/uni-app'


const data = ref('')

onLoad(async (e:any) => {
    await contentProtocol().then((res: any) => {
		if(e.type === '1') {
			data.value = res.data.protocol
		} else {
			data.value = res.data.privacy
		}
    })
})
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx;

}

</style>
