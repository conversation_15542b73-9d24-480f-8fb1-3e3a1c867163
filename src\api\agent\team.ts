import { request } from '@/config/request'

export interface listParamsType {
    filter_version?: string
    filter_search?: string
    filter_policy_level?: string
    filter_agent_identity?: string
    filter_new_merchant?: string
    order_key: string
    order_type: string
}

export function list(data: listParamsType) {
    return request.http({
        url: '/api/d.agentUser/getTeamList',
        data
    })
}

export function detail(uid: number) {
    return request.http({
        url: '/api/d.agentUser/getAgentDetails',
        data: { uid }
    })
}
