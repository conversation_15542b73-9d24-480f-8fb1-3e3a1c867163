import { request } from '@/config/request'

export interface overviewType {
    uid?: number
    terminal_type: string
    begin_time: number
    end_time: number
    terminal_brand: string
}

export function overview(data: overviewType) {
    return request.http({
        url: `/api/d.transaction/overview`,
        data
    })
}

export function memberDetails(data: overviewType) {
    return request.http({
        url: `/api/d.transaction/memberDetails`,
        data
    })
}

// 激活彪榜
export function listOfApi(data: { type: string; start_time: number; end_time: number }) {
    return request.http({
        url: `/api/d.agentUserTerminal/activateBid`,
        data
    })
}

// 狂飙计划列表
export function hurricanePlan(data: { type: string }) {
    return request.http({
        url: `/api/d.agentUser/hurricanePlan`,
        data
    })
}

// 狂飙计划  是否能创建战队
export function whetherCreateTeam() {
    return request.http({
        url: `/api/d.agentUser/whetherCreateTeam`
    })
}

// 获取地区
export function gatCreateTeamRegion() {
    return request.http({
        url: `/api/d.agentUser/gatCreateTeamRegion`
    })
}

// 提交申请战队
export function createTeam(data: { name: string; slogan: string; region: string }) {
    return request.http({
        url: `/api/d.agentUser/createTeam`,
        data
    })
}
