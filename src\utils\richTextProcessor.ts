/**
 * 富文本处理工具函数
 * 用于处理富文本中的HTML标签样式
 */

/**
 * 处理img标签，确保图片响应式显示
 * @param content 富文本内容
 * @returns 处理后的富文本内容
 */
export function processImageTags(content: string): string {
    return content.replace(/<img([^>]*)>/g, function (match: string, p1: string) {
        // 处理img标签的样式
        if (/style\s*=\s*['"][^'"]*width\s*:[^'"]*['"]/i.test(p1)) {
            // 如果已有width定义，确保有max-width
            const result = p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, function (styleMatch: string, start: string, styles: string, end: string) {
                if (!styles.includes('max-width')) {
                    return start + styles + '; max-width: 100%;' + end
                }
                return styleMatch
            })
            return '<img' + result + '>'
        } else if (/style\s*=\s*['"]/.test(p1)) {
            // 有style但无width，直接在末尾追加
            return '<img' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; width: 100%; max-width: 100%;$2') + '>'
        } else {
            // 无style属性，直接添加
            return '<img' + p1 + ' style="width: 100%; max-width: 100%;">'
        }
    })
}

/**
 * 处理p标签，移除背景色
 * @param content 富文本内容
 * @returns 处理后的富文本内容
 */
export function processParagraphTags(content: string): string {
    return content.replace(/<p([^>]*)>/g, function (match: string, p1: string) {
        if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
            // 如果已有background-color定义，先移除再追加（避免重复）
            return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
        } else if (/style\s*=\s*['"]/.test(p1)) {
            // 有style但无background-color，直接在末尾追加
            return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
        } else {
            // 无style属性，直接添加
            return '<p' + p1 + ' style="background-color: transparent!important;">'
        }
    })
}

/**
 * 处理span标签，移除背景色
 * @param content 富文本内容
 * @returns 处理后的富文本内容
 */
export function processSpanTags(content: string): string {
    return content.replace(/<span([^>]*)>/g, function (match: string, p1: string) {
        if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
            // 如果已有background-color定义，先移除再追加（避免重复）
            return '<span' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
        } else if (/style\s*=\s*['"]/.test(p1)) {
            // 有style但无background-color，直接在末尾追加
            return '<span' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
        } else {
            // 无style属性，直接添加
            return '<span' + p1 + ' style="background-color: transparent!important;">'
        }
    })
}

/**
 * 处理video标签，确保视频响应式显示
 * @param content 富文本内容
 * @returns 处理后的富文本内容
 */
export function processVideoTags(content: string): string {
    return content.replace(/<video([^>]*)>/g, function (match: string, p1: string) {
        if (/style\s*=\s*['"]/.test(p1)) {
            // 有style属性，在末尾追加
            return '<video' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; width: 100%; max-width: 100%; height: auto;$2') + '>'
        } else {
            // 无style属性，直接添加
            return '<video' + p1 + ' style="width: 100%; max-width: 100%; height: auto;">'
        }
    })
}

/**
 * 处理富文本内容，应用所有样式处理
 * @param content 原始富文本内容
 * @returns 处理后的富文本内容
 */
export function processRichText(content: string): string {
    let processedContent = content

    // 依次处理各种标签
    processedContent = processImageTags(processedContent)
    processedContent = processParagraphTags(processedContent)
    processedContent = processSpanTags(processedContent)
    processedContent = processVideoTags(processedContent)

    return processedContent
}

/**
 * 智能添加或修改HTML标签的style属性
 * @param tagContent 标签内容（不包括<>）
 * @param newStyles 要添加的样式对象
 * @returns 处理后的标签内容
 */
export function addOrUpdateStyles(tagContent: string, newStyles: Record<string, string>): string {
    const styleRegex = /(style\s*=\s*['"])([^'"]*)(['"])/i
    const hasStyle = styleRegex.test(tagContent)

    if (hasStyle) {
        // 已有style属性，需要智能合并
        return tagContent.replace(styleRegex, function (match: string, start: string, existingStyles: string, end: string) {
            let updatedStyles = existingStyles

            // 处理每个新样式
            Object.entries(newStyles).forEach(([property, value]) => {
                const propertyRegex = new RegExp(`\\b${property}\\s*:\\s*[^;]*`, 'i')
                if (propertyRegex.test(updatedStyles)) {
                    // 如果已存在该属性，替换它
                    updatedStyles = updatedStyles.replace(propertyRegex, `${property}: ${value}`)
                } else {
                    // 如果不存在该属性，添加它
                    updatedStyles += `; ${property}: ${value}`
                }
            })

            return start + updatedStyles + end
        })
    } else {
        // 没有style属性，直接添加
        const styleString = Object.entries(newStyles)
            .map(([property, value]) => `${property}: ${value}`)
            .join('; ')
        return tagContent + ` style="${styleString}"`
    }
}
