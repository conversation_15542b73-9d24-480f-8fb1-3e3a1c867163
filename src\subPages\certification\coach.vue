<script setup lang="ts">
import { roleInfo, uploadFile } from '@/api/common/common'
import { authentication } from '@/api/mine/user'
import { coachInfoInt } from '@/api/mine/type'
import { getImageUrl } from '@/utils/common'
import useUserStore from '@/store/user'
const userStore = useUserStore()
const form = ref()
const formData = reactive<coachInfoInt>({
    name: '',
    sex: 1,
    mobile: '',
    role_id: null,
    title: '',
    num: '',
    image_path: '',
    photo_path: '',
    introduce: ''
})
const roleList = ref<{ role_id: number; name: string }[]>([])
const sexList = ref([
    { name: '男', value: 1 },
    { name: '女', value: 2 }
])
const rules = reactive({
    name: {
        type: 'string',
        required: true,
        message: '请输入姓名',
        trigger: ['blur', 'change']
    },
    mobile: [
        {
            required: true,
            message: '请输入手机号',
            trigger: ['change', 'blur']
        },
        {
            validator: (_rule: any, value: any, _callback: Function) => {
                return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            trigger: ['change', 'blur']
        }
    ],
    role_id: {
        type: 'number',
        required: true,
        message: '请选择认证等级',
        trigger: ['blur', 'change']
    },
    title: {
        type: 'string',
        required: true,
        message: '请输入证书名称',
        trigger: ['blur', 'change']
    },
    num: {
        type: 'string',
        required: true,
        message: '请输入证书编号',
        trigger: ['blur', 'change']
    },
    image_path: {
        type: 'string',
        required: true,
        message: '请上传证书图片',
        trigger: ['blur', 'change']
    },
    photo_path: {
        type: 'string',
        required: true,
        message: '请上传照片',
        trigger: ['blur', 'change']
    },
    introduce: {
        type: 'string',
        required: true,
        message: '请输入个人介绍',
        trigger: ['blur', 'change']
    }
})

const sexChange = (value: number) => (formData.sex = sexList.value[value].value)

const isShowSheet = ref(false)
const getRoleInfo = () => {
    roleInfo().then((res: any) => {
        roleList.value = res.data
    })
}
const selectRole = (item: any) => (formData.role_id = item.role_id)
const getRoleName = computed(() => {
    const role = roleList.value.find((item: any) => item.role_id === formData.role_id)
    return role ? role.name : ''
})

const fileList = ref<any[]>([])
const fileList1 = ref<any[]>([])
const deletePic = (event: { index: number }) => {
    formData.image_path = ''
    fileList.value.splice(event.index, 1)
}
const afterRead = async (event: any) => {
    const lists: any = [].concat(event.file)
    let fileListLen = fileList.value.length
    lists.map((item: any) => {
        fileList.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        })
    })
    for (let i = 0; i < lists.length; i++) {
        const result: any = await uploadFile(lists[i].url)
        let item = fileList.value[fileListLen]
        formData.image_path = result.data.url
        fileList.value.splice(
            fileListLen,
            1,
            Object.assign(item, {
                status: 'success',
                message: '',
                url: getImageUrl(result.data.url),
                urls: result.data.url
            })
        )
        fileListLen++
    }
}

const deletePic1 = (event: { index: number }) => {
    formData.photo_path = ''
    fileList1.value.splice(event.index, 1)
}
const afterRead1 = async (event: any) => {
    const lists: any = [].concat(event.file)
    let fileListLen = fileList1.value.length
    lists.map((item: any) => {
        fileList1.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        })
    })
    for (let i = 0; i < lists.length; i++) {
        const result: any = await uploadFile(lists[i].url)
        let item = fileList1.value[fileListLen]
        formData.photo_path = result.data.url
        fileList1.value.splice(
            fileListLen,
            1,
            Object.assign(item, {
                status: 'success',
                message: '',
                url: getImageUrl(result.data.url),
                urls: result.data.url
            })
        )
        fileListLen++
    }
}

const submit = () => {
    form.value
        .validate()
        .then((valid: boolean) => {
            if (valid) {
                uni.showToast({ title: '校验成功', icon: 'none', mask: true })
                authentication(formData).then(() => {
                    uni.showToast({ title: '提交成功', icon: 'none', mask: true })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                })
            } else {
                // uni.showToast({ title: '校验失败', icon: 'none', mask: true })
            }
        })
        .catch(() => {
            // 处理验证错误
            // uni.showToast({ title: '校验失败', icon: 'none', mask: true })
        })
}

// const isShowPopup = ref(true)
// const showModal = () => (isShowPopup.value = true)
// const updateMobile = () => {
//     if (formData.mobile.trim() === '') {
//         uni.showToast({ title: '手机号不能为空', icon: 'none', mask: true })
//     } else if (!uni.$u.test.mobile(formData.mobile)) {
//         uni.showToast({ title: '手机号格式不正确', icon: 'none', mask: true })
//     } else {
//         upMobile({ mobile: formData.mobile }).then(() => {
//             uni.showToast({ title: '修改成功', icon: 'none', mask: true })
//             userStore.getUserInfo().then((res) => {
//                 console.log(123123, res)
//             })
//         })
//     }
//     isShowPopup.value = false
// }

// const closePopup = () => {
//     if (formData.mobile.trim() === '') {
//         formData.mobile = userStore.userInfo.mobile
//     } else if (!uni.$u.test.mobile(formData.mobile)) {
//         formData.mobile = userStore.userInfo.mobile
//     }
//     isShowPopup.value = false
// }

onMounted(() => {
    getRoleInfo()
    formData.mobile = userStore.userInfo.mobile
})
</script>
<template>
    <view class="coach">
        <ex-header title="教练认证" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="title">确认个人身份信息</view>
        <view class="content">
            <u-form ref="form" :model="formData" :rules="rules" :label-style="{ fontSize: '30rpx', color: '#fff' }" label-width="140rpx">
                <u-form-item label="姓名" border-bottom prop="name" required>
                    <u-input v-model="formData.name" border="none" clearable color="#fff" placeholder="请输入姓名" maxlength="10" />
                </u-form-item>
                <u-form-item label="性别" border-bottom prop="sex" required>
                    <view class="sex">
                        <u-subsection :list="sexList" :current="formData.sex - 1" @change="sexChange" />
                    </view>
                </u-form-item>
                <u-form-item label="手机号" border-bottom prop="mobile" required>
                    <u-input v-model="formData.mobile" border="none" readonly color="#fff" placeholder="请输入手机号">
                        <!-- <template #suffix>
                            <view style="color: #169bd5" @tap="showModal">修改手机号</view>
                        </template> -->
                    </u-input>
                </u-form-item>
                <u-form-item label="认证等级" border-bottom prop="role_id" required @tap="isShowSheet = true">
                    <u-input :model-value="getRoleName" border="none" readonly color="#fff" placeholder="请选择认证等级" />
                </u-form-item>
                <u-form-item label="证书名称" border-bottom prop="title" required>
                    <u-input v-model="formData.title" border="none" clearable color="#fff" placeholder="请输入证书名称" />
                </u-form-item>
                <u-form-item label="证书编号" border-bottom prop="num" required>
                    <u-input v-model="formData.num" border="none" clearable color="#fff" placeholder="请输入证书编号" />
                </u-form-item>
                <u-form-item label="证书上传" border-bottom prop="image_path" required>
                    <view class="grid">
                        <u-upload
                            accept="image"
                            :file-list="fileList"
                            @afterRead="afterRead"
                            @delete="deletePic"
                            multiple
                            :max-count="1"
                            width="60"
                            height="60"
                        />
                    </view>
                </u-form-item>
                <u-form-item label="照片" border-bottom prop="photo_path" required>
                    <view class="grid">
                        <u-upload
                            accept="image"
                            :file-list="fileList1"
                            @afterRead="afterRead1"
                            @delete="deletePic1"
                            multiple
                            :max-count="1"
                            width="60"
                            height="60"
                        />
                    </view>
                </u-form-item>
                <u-form-item label="个人介绍" border-bottom prop="introduce" required>
                    <u-input v-model="formData.introduce" border="none" clearable color="#fff" placeholder="请输入个人介绍" />
                </u-form-item>
            </u-form>
        </view>
        <u-action-sheet
            :actions="roleList"
            :title="'认证等级'"
            :show="isShowSheet"
            close-on-click-overlay
            cancel-text="取消"
            @close="isShowSheet = false"
            @select="selectRole"
        />
        <u-button class="submit" @tap="submit">提交认证</u-button>
        <!-- <u-popup :show="isShowPopup" mode="center" round="12" @close="closePopup">
            <view class="popup_box">
                <view class="tit">修改手机号</view>
                <u-input placeholder="请输入手机号" border="surround" v-model="formData.mobile" clearable />
                <view class="buttons" @tap="updateMobile">修改</view>
            </view>
        </u-popup> -->
    </view>
</template>

<style scoped lang="scss">
.coach {
    .title {
        padding: 30rpx;
        font-size: 36rpx;
        color: #ffffff;
    }

    .content {
        padding: 10rpx 40rpx 180rpx;

        .sex {
            width: 50%;
        }

        .grid {
            width: fit-content;
        }

        :deep(.u-form-item__body__right__content__slot) {
            justify-content: flex-end;
        }

        .buttons {
            color: #2980ad;
        }

        :deep(.u-upload__button) {
            margin: 0;
        }
    }

    :deep(.u-button) {
        position: fixed;
        bottom: 30rpx;
        left: 0;
        right: 0;
        margin: 0 auto;
        border-radius: 90rpx;
        width: 90vw;
    }

    .popup_box {
        padding: 30rpx;
        width: 80vw;
        border-radius: 12rpx;

        .tit {
            text-align: center;
            font-weight: 700;
            font-size: 30rpx;
            margin-bottom: 30rpx;
        }

        .buttons {
            text-align: center;
            font-size: 30rpx;
            color: #fff;
            background-color: #25a3dd;
            margin-top: 30rpx;
            border-radius: 90rpx;
            padding: 20rpx 0;
        }
    }
}
</style>
