// 定义组合式API仓库
import { defineStore } from 'pinia'
import { getPrefixName } from '@/config'
import { getUserInfos, getMemberLevel } from '@/api/login/index'

interface userInfoStoreInt {
    [n: string]: any
}

interface addressType {
    id: number
    name: string
    children?: addressType[]
}

export default defineStore(
    getPrefixName('user'),
    () => {
        const token = ref('')
        const userInfo = ref<userInfoStoreInt>({})
        const memberLevelList = ref<any[]>([])
        const address = ref<addressType[]>()
        const checkLogin = computed(() => token.value !== '')
        function getUserInfo() {
            return new Promise((resolve, reject) => {
                getUserInfos()
                    .then(({ data }: any) => {
                        userInfo.value = data ?? null
                        console.log(123123123, userInfo.value)
                        resolve({ data })
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }

        function getMemberLevelList() {
            return new Promise((resolve, reject) => {
                getMemberLevel()
                    .then(({ data }: any) => {
                        memberLevelList.value = data ?? null
                        resolve({ data })
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }

        function logOut() {
            token.value = ''
            userInfo.value = {}
            uni.clearStorageSync()
            uni.showToast({
                icon: 'none',
                title: '退出成功',
                mask: true,
                success() {
                    setTimeout(() => uni.switchTab({ url: '/pages/index/index' }), 1500)
                }
            })
        }

        return {
            token,
            userInfo,
            memberLevelList,
            address,
            checkLogin,
            getUserInfo,
            getMemberLevelList,
            logOut
        }
    },
    {
        unistorage: {
            serializer: {
                // 序列化，默认为 JSON.stringify
                serialize(v) {
                    return JSON.stringify(v)
                },
                // 反序列化，默认为 JSON.parse
                deserialize(v) {
                    return JSON.parse(v)
                }
            }
        } // 开启后对 state 的数据读写都将持久化
        // unistorage: {
        //     key: 'userInfo', // 缓存的键，默认为该 store 的 id，这里是 main,
        //     paths: ['userInfo.token'], // 需要缓存的路径，这里设置 foo 和 nested 下的 data 会被缓存
        //     // 初始化恢复前触发
        //     beforeRestore(ctx: any) {
        //         console.log(ctx)
        //     },
        //     // 初始化恢复后触发
        //     afterRestore(ctx: any) {
        //         console.log('ctx', ctx)
        //     },

        // },
    }
)
