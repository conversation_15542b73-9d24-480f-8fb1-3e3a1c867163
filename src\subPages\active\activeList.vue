<template>
    <u-navbar
        title="课程"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <u-search
            placeholder=""
            v-model="keyword"
            bg-color="#f7f8fa"
            action-text="全部"
            :action-style="{ fontFamily: 'Source Han Sans', fontSize: '28rpx', color: '#FFFFFF' }"
        >
            <image style="width: 40rpx; height: 40rpx" src="@/static/more.png" mode=""></image>
        </u-search>
        <view class="kcbox">
            <view class="kcone" v-for="(item, index) in kcList" :key="index" @click="godetail(item.id)">
                <view class="bm" :style="item.status == '报名中' ? 'background-color: #25A3DD' : 'background-color: #AAAAAA'">
                    {{ item.status }}
                </view>
                <image style="width: 100%; height: 366rpx; border-radius: 10rpx" :src="item.pic" mode=""></image>
                <text class="title">{{ item.title }}</text>
                <text class="time">培训时间：{{ item.time }}</text>
                <text class="ms">{{ item.message }}</text>
                <view class="bottompart">
                    <view class="class">
                        {{ item.class }}
                    </view>
                    <view class="more">查看更多 >></view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const keyword = ref('')

const kcList = ref([
    {
        id: 1,
        pic: '/static/sl1.png',
        status: '报名中',
        title: 'LNT全国户外安全教育计划课程',
        time: '2025-05-22',
        message:
            '活动简述简述简述简述简述活动简述简述简述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述简述简述简述简述',
        class: '环保活动'
    },
    {
        id: 1,
        pic: '/static/sl1.png',
        status: '已结束',
        title: 'LNT全国户外安全教育计划课程',
        time: '2025-05-22',
        message:
            '活动简述简述简述简述简述活动简述简述简述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述简述简述简述简述',
        class: '环保活动'
    },
    {
        id: 1,
        pic: '/static/sl1.png',
        status: '已满员',
        title: 'LNT全国户外安全教育计划课程',
        time: '2025-05-22',
        message:
            '活动简述简述简述简述简述活动简述简述简述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述活动简述述简述简述简述简述简述简述简述',
        class: '环保活动'
    }
])

const godetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/active/activeDetail?id=' + id
    })
}
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    height: calc(100vh - 174rpx);
    background: #0e2942;
    display: grid;
    align-content: flex-start;
    padding: 20rpx 36rpx;
    box-sizing: border-box;

    :deep(.u-search) {
        height: 62rpx;
        border-radius: 66rpx;
    }

    .kcbox {
        padding: 0 20rpx 40rpx 20rpx;
        box-sizing: border-box;
        overflow-y: auto;

        :first-child {
            margin-top: 0 !important;
        }

        .kcone {
            display: grid;
            margin-top: 60rpx;
            border-radius: 20rpx;

            .bm {
                width: 100rpx;
                height: 40rpx;
                border-radius: 40rpx;
                font-family: Source Han Sans;
                font-size: 26rpx;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                letter-spacing: normal;
                color: #ffffff;
                position: relative;
                top: 40rpx;
            }

            .title {
                margin-top: 32rpx;
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .time {
                margin-top: 8rpx;
                font-family: Source Han Sans;
                font-size: 24rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .ms {
                margin-top: 6rpx;
                font-family: Source Han Sans;
                font-size: 24rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
                /* 根据实际需要设置宽度 */
            }

            .bottompart {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 18rpx;

                .class {
                    padding: 20rpx;
                    height: 42rpx;
                    border-radius: 40rpx;
                    background-color: #8cc046;
                    font-family: Source Han Sans;
                    font-size: 26rpx;
                    font-weight: normal;
                    line-height: normal;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .more {
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #25a3dd;
                }
            }
        }
    }
}
</style>
