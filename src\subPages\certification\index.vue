<script setup lang="ts">
const navto = (url: string, params = {}) => uni.$util.goToPage({ url, params })
</script>
<template>
    <view class="certification">
        <ex-header title="认证" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="flex column content">
            <view class="title">
                欢迎加入
                <text>LNT中国</text>
                （无痕山林） 我们参考LNT标准开展无痕活动
            </view>
            <view class="desc">和我们一起，用最轻的脚步走过自然</view>
            <image src="@/static/rzlogo.png" mode="widthFix" />
            <u-button height="30" shape="circle" @tap="navto('subPages/certification/coach')">教练认证</u-button>
            <u-button color="#25A3DD" shape="circle" @tap="navto('subPages/certification/mine')">我的认证</u-button>
        </view>
    </view>
</template>

<style scoped lang="scss">
.certification {
    .content {
        padding: 30rpx;

        .title {
            font-size: 36rpx;
            color: #ffffff;

            text {
                color: #25a3dd;
            }
        }

        .desc {
            margin-top: 24rpx;
            width: 100%;
            font-size: 36rpx;
            color: #25a3dd;
        }

        image {
            width: 70vw;
            margin: 150rpx 0 60rpx;
        }

        :deep(.u-button) {
            height: 70rpx;
            margin-top: 20rpx;
            width: 60vw;
        }

        .gz {
            margin-top: 90rpx;
            color: #fff;
        }
    }
}
</style>
