<template>
    <ex-header title="课程详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />
    <view class="classbox">
        <view class="piccard">
            <view class="leven">{{ details.course_role_name }}</view>
            <image style="width: 100%; height: 348rpx" :src="getImageUrl(details.index_pic)" mode=""></image>
        </view>
        <view class="headpart">
            <text class="title">{{ details.title }}</text>
            <text class="time">开始时间：{{ details.studyStart_time }}</text>
            <view class="levclass">{{ details.course_role_name }}</view>
        </view>
        <view class="mok">课程详情</view>
        <view class="address">培训地点：{{ details.address }}</view>
        <view class="price">价格：￥{{ details.price }}</view>
        <view class="zq">周期：{{ details.studyStart_time }} ~ {{ details.studyEnd_time }}</view>
        <mp-html :content="details.content" />
        <template v-if="details.coach_name">
            <view class="mok">授课教练</view>
            <view class="jlcard">
                <image style="width: 100rpx; height: 100rpx; border-radius: 50%" :src="getHeaderImage(details.coach_head_pic)" mode="" />
                <view class="message">
                    <view class="namepart">
                        <text class="name">{{ details.coach_name || '暂无教练' }}</text>
                        <view class="rz">{{ details.coach_role_name || '暂无职位' }}</view>
                    </view>
                    <text class="js">{{ details.coach_introduce || '暂无介绍' }}</text>
                </view>
            </view>
        </template>
        <view class="bottom">
            <button style="width: 10%" open-type="share" @tap.stop="handleShareClick">
                <image style="width: 40rpx; height: 40rpx" src="@/static/share.png" mode=""></image>
            </button>
            <view class="but" @tap="operation">{{ details.statusArr?.name === '报名中' ? '开始课程' : details.statusArr?.name }}</view>
        </view>
    </view>
    <u-modal :show="showtc" title="提示" show-cancel-button="true" :content="contentMsg" @confirm="confirm" @cancel="cancel"></u-modal>
</template>

<script setup lang="ts">
import useUserStore from '@/store/user'
import { getHeaderImage, getImageUrl } from '@/utils/common'
import { getCourseDetail, createOrder } from '@/api/course/index'
const userStore = useUserStore()
const showtc = ref(false)
const contentMsg = ref('')
const confirm = () => {
    if (userStore.userInfo.is_certification === 0) {
        uni.navigateTo({ url: '/subPages/index/improveInformation' })
    } else if (userStore.userInfo.role_id + 1 < details.value.role_id) {
        console.log('上传证书')
    }
    showtc.value = false
}

const cancel = () => {
    showtc.value = false
}

const details = ref<any>({})

const getDetail = async (id: number) => {
    const res = (await getCourseDetail(id)) as { data: { [n: string]: string } }
    res.data.content = res.data.content
        .replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
        .replace(/<p([^>]*)>/g, function (match, p1) {
            if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                // 如果已有background-color定义，先移除再追加（避免重复）
                return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
            } else if (/style\s*=\s*['"]/.test(p1)) {
                // 有style但无background-color，直接在末尾追加
                return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
            } else {
                // 无style属性，直接添加
                return '<p' + p1 + ' style="background-color: transparent!important;">'
            }
        })
        .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
    details.value = res.data
    console.log(details.value)
    if (details.value.statusArr.id === 2) {
        if (userStore.checkLogin && userStore.userInfo.is_certification === 0) {
            // uni.navigateTo({ url: '/subPages/index/improveInformation' })
            uni.showModal({
                title: '提示',
                content: '请先前往个人信息页面完成实名认证，否则无法正常获取报名信息',
                confirmText: '前往',
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({ url: '/subPages/index/improveInformation' })
                    }
                }
            })
        }
    }
    if (userStore.userInfo.is_certification === 0) {
        contentMsg.value = '请先前往个人信息页面完成实名认证，否则无法正常获取报名信息'
    } else if (userStore.userInfo.role_id + 1 < details.value.role_id) {
        contentMsg.value = `您是${roleName(userStore.userInfo.role_id)}级别用户，无法购买${details.value.course_role_name}级别课程，请上传${details.value.course_role_name}级别证书认证`
    }
}

const operation = () => {
    switch (details.value.statusArr.id) {
        case 2:
            if (userStore.userInfo.is_certification === 0 || userStore.userInfo.role_id + 1 < details.value.role_id) {
                showtc.value = true
            } else {
                createOrder({ course_id: details.value.id }).then((res: any) => {
                    const { order_id, trade_type } = res.data
                    uni.navigateTo({ url: `/subPages/class/orderDetail?order_id=${order_id}&trade_type=${trade_type}` })
                })
            }
            break
        default:
            uni.showToast({ title: statusMsg.value, icon: 'none', mask: true })
            break
    }
}

const statusMsg = computed(() => {
    if (details.value.statusArr.id === 1) {
        return '该课程还未开始报名,请选择其他课程'
    } else if (details.value.statusArr.id === 3) {
        return '该课程正在进行中,请选择其他课程'
    } else if (details.value.statusArr.id === 4) {
        return '该课程报名已结束,请选择其他课程'
    }
})

// 处理分享按钮点击
const handleShareClick = (event: Event) => {
    event.stopPropagation() // 阻止事件冒泡
}

const roleName = (id: number) => {
    switch (id) {
        case 1:
            return '技能课'
        case 2:
            return 'L1'
        case 3:
            return 'L2'
        case 4:
            return 'L3'
        default:
            return '普通'
    }
}

const detId = ref()

onLoad((opt) => {
    if (opt?.id) {
        detId.value = opt.id
    }
})

onShow(() => {
    getDetail(detId.value)
})
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx 30rpx 150rpx;
    box-sizing: border-box;

    .piccard {
        .leven {
            position: absolute;
            top: 228rpx;
            padding: 6rpx 12rpx;
            background-color: #1a2b4a;
            font-size: 24rpx;
            color: #ffffff;
            border-radius: 0 30rpx 30rpx 0;
        }
    }

    .headpart {
        margin-top: 18rpx;
        display: grid;
        align-items: center;
        justify-items: center;

        .title {
            font-family: Source Han Sans;
            font-size: 32rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .time {
            margin-top: 10rpx;
            font-family: Source Han Sans;
            font-size: 24rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: 2rpx;
            color: #ffffff;
        }

        .levclass {
            margin-top: 8rpx;
            width: 192rpx;
            height: 42rpx;
            font-family: Source Han Sans;
            font-size: 26rpx;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: normal;
            color: #25a3dd;
            background-color: #ffffff;
            border-radius: 20rpx;
        }
    }

    .mok {
        margin-top: 26rpx;
        width: 150rpx;
        height: 46rpx;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: normal;
        color: #1a2b4a;
        border-radius: 5rpx;
        background-color: #ffffff;
    }

    .txt {
        font-size: 24rpx;
        color: #ffffff;
    }

    .address {
        margin-top: 20rpx;
        font-family: Source Han Sans;
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 2rpx;
        color: #ffffff;
    }

    .price {
        margin-top: 20rpx;
        font-family: Source Han Sans;
        font-size: 24rpx;
        font-weight: bold;
        line-height: normal;
        letter-spacing: normal;
        color: #ffffff;
    }

    .zq {
        margin: 20rpx 0;
        font-family: Source Han Sans;
        font-size: 24rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 2rpx;
        color: #ffffff;
    }

    .skjs {
        margin-top: 48rpx;
        width: 192rpx;
        height: 42rpx;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        letter-spacing: normal;
        color: #25a3dd;
        background-color: #ffffff;
        border-radius: 20rpx;
    }

    .jlcard {
        margin-top: 40rpx;
        width: 100%;
        height: 144rpx;
        border-radius: 22rpx;
        background: rgba(255, 255, 255, 0.298);
        display: flex;
        align-items: center;
        padding: 20rpx;
        box-sizing: border-box;

        .message {
            max-width: 80%;
            display: grid;
            margin-left: 30rpx;

            .namepart {
                display: flex;

                .name {
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .rz {
                    padding: 4rpx 20rpx;
                    border-radius: 58rpx;
                    background: #8cc046;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    letter-spacing: normal;
                    color: #ffffff;
                    margin-left: 50rpx;
                }
            }

            .js {
                margin-top: 8rpx;
                font-family: Source Han Sans;
                font-size: 24rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                width: 100%;
            }
        }
    }

    .bottom {
        position: fixed;
        bottom: 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .but {
            margin-left: 32rpx;
            width: 602rpx;
            height: 78rpx;
            font-family: Source Han Sans;
            font-size: 26rpx;
            font-weight: normal;
            line-height: normal;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: normal;
            color: #1a2b4a;
            border-radius: 40rpx;
            background-color: #ffffff;
        }
    }
}
</style>
