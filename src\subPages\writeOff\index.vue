<script setup lang="ts">
import { signIn, signOut } from '@/api/mine/user'
const scanCode = (type: number) => {
    uni.scanCode({
        scanType: ['qrCode'],
        success: (res) => {
            console.log(res)
            const { result } = res
            if (type === 1) {
                signIn({ verification_code: result }).then(() => {
                    uni.showToast({ title: '签到成功', icon: 'none', mask: true })
                })
            } else if (type === 2) {
                signOut({ verification_code: result }).then(() => {
                    uni.showToast({ title: '签退成功', icon: 'none', mask: true })
                })
            }
        },
        fail: (res) => {
            uni.showToast({ title: '核验失败', icon: 'none', mask: true })
            console.log(res)
        }
    })
}
</script>
<template>
    <view class="write_off">
        <ex-header title="扫码核销" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="flex column content">
            <image src="@/static/rzlogo.png" mode="widthFix" />
            <u-button height="30" shape="circle" @tap="scanCode(1)">活动签到</u-button>
            <u-button color="#25A3DD" shape="circle" @tap="scanCode(2)">活动签退</u-button>
        </view>
    </view>
</template>

<style scoped lang="scss">
.write_off {
    .content {
        padding: 30rpx;

        .title {
            font-size: 36rpx;
            color: #ffffff;

            text {
                color: #25a3dd;
            }
        }

        .desc {
            margin-top: 24rpx;
            width: 100%;
            font-size: 36rpx;
            color: #25a3dd;
        }

        image {
            width: 70vw;
            margin: 150rpx 0 200rpx;
        }

        :deep(.u-button) {
            height: 70rpx;
            margin-top: 20rpx;
            width: 60vw;
        }

        .gz {
            margin-top: 90rpx;
            color: #fff;
        }
    }
}
</style>
