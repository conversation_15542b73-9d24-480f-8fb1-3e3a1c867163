import { request } from '@/config/request'

export interface listType {
    search: string
    begin_time: number
    end_time: number
    status: string
    limit: number
    page: number
}

export function publicDiskList(data: pageType) {
    return request.http({
        url: '/api/d.fissionUserOrders/publicDiskList',
        data
    })
}

export function orderGrabbing(id: number) {
    return request.http({
        url: '/api/d.fissionUserOrders/orderGrabbing',
        data: { id }
    })
}

export function list(data: listType) {
    return request.http({
        url: '/api/d.fissionUserOrders/myList',
        data
    })
}

export function detail(id: number) {
    return request.http({
        url: '/api/d.fissionUserOrders/details',
        data: { id }
    })
}

export function processOrder(data: { id: number; result: string }) {
    return request.http({
        url: '/api/d.fissionUserOrders/processOrder',
        data
    })
}

export function myListCount() {
    return request.http({
        url: '/api/d.fissionUserOrders/myListCount'
    })
}
