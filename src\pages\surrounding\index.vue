<script setup lang="ts">
import { reactive, ref } from 'vue'
import exTabbar from '@/components/ex-tabbar/ex-tabbar.vue'
import { categoryList, goodsPages } from '@/api/shop/index'
import { getImageUrl } from '@/utils/common'
import { useShare } from '@/composables/useShare'
import { onShareAppMessage, onShareTimeline, onShow } from '@dcloudio/uni-app'

// 使用全局分享功能 - 周边页面跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: 'LNT(中国) - 精品周边商品，等你来选购！',
    imageUrl: '/static/logo.png',
    path: '/pages/surrounding/index',
    redirectToHome: true // 周边页面分享后跳转到首页
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})

const tabsList = ref([])

const orderListRef = ref()
const query = reactive({ keyword: '', goods_category: '' })

// 定义方法
function tabclick(e: any) {
    query.goods_category = e.category_id
    orderListRef.value.refreshFn()
}

// const current = ref(0)
// const swiperList = ref([
// 	'https://www.2008php.com/2011_Website_appreciate/2011-09-23/20110923163645.jpg',
// 	'https://www.keaitupian.cn/cjpic/frombd/1/253/174051936/621369760.jpg',
// 	'https://img95.699pic.com/photo/50464/9776.jpg_wh860.jpg'
// ])

// const swiperchange = (e : any) => {
// 	// console.log(e)
// 	current.value = e.current
// }

const search = uni.$util.debounce(() => {
    orderListRef.value.refreshFn()
})

const doSearch = (formData: { page: number; limit: number; keyword: string; goods_category: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    goodsPages(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        console.log(data)
        onSuccess({ data })
    })
}

const goshopping = () => {
    uni.navigateTo({ url: '/subPages/shop/shopping' })
}

const godetail = (id: string) => {
    uni.navigateTo({
        url: '/subPages/shop/shopDetail?id=' + id
    })
}

onShow(async () => {
    // 处理分享参数
    await handleShareParams()

    await categoryList().then((res: any) => {
        let arr = [{ category_id: '', category_name: '全部商品' }]
        tabsList.value = [...arr, ...res.data]
    })
})
</script>
<template>
    <view class="surrounding">
        <ex-header title="商店" background-color="#1a2b4a" text-color="#fff">
            <template #left>
                <image style="width: 160rpx" src="@/static/img/icon1.png" mode="aspectFill"></image>
            </template>
        </ex-header>
        <view class="searchbox">
            <image style="width: 50rpx; height: 50rpx; margin-right: 22rpx" src="@/static/gw.png" mode="" @click="goshopping"></image>
            <u-search
                placeholder=""
                v-model="query.keyword"
                bg-color="#f7f8fa"
                action-text="搜索"
                @custom="search"
                :action-style="{ fontFamily: ' Source Han Sans', fontSize: '28rpx', color: '#FFFFFF' }"
            ></u-search>
        </view>
        <u-tabs
            :list="tabsList"
            @click="tabclick"
            line-color="#00BBF0"
            key-name="category_name"
            line-width="30"
            :active-style="{ color: '#AAAAAA', fontFamily: 'Source Han Sans', fontSize: '30rpx' }"
            :inactive-style="{ color: '#AAAAAA', fontFamily: 'Source Han Sans', fontSize: '30rpx' }"
        ></u-tabs>
        <!-- <u-swiper
            :list="swiperList"
            :indicator="true"
            current="current"
            indicator-style="bottom"
            circular
            radius="0"
            height="220"
            @change="swiperchange"
        >
            <template #indicator>
                <view class="indicator">
                    <view
                        class="indicator__dot"
                        v-for="(item, index) in swiperList"
                        :key="index"
                        :class="[index === current && 'indicator__dot--active']"
                    ></view>
                </view>
            </template>
        </u-swiper> -->
        <view class="main">
            <view class="qbsp">全部商品</view>
            <ex-list ref="orderListRef" custom-list-type="custom" style="width: 100%" :options="query" :on-form-search="doSearch">
                <template #default="{ data }">
                    <view class="shoplist">
                        <view class="shopone" v-for="(v, k) in data" :key="k" @click="godetail(v.goods_id)">
                            <image class="img" :src="getImageUrl(v.goods_cover)" mode="aspectFill" />
                            <text class="title">{{ v.goods_name }}</text>
                            <text class="txt">{{ v.sub_title }}</text>
                            <view class="ppat">
                                <text class="price">会员价：{{ v.goodsSku.sale_price }}</text>
                            </view>
                        </view>
                    </view>
                </template>
            </ex-list>
        </view>
        <ex-tabbar current="surrounding"></ex-tabbar>
    </view>
</template>

<style scoped lang="scss">
.surrounding {
    padding-bottom: 160rpx;

    :deep(.u-tabs__wrapper__nav__item) {
        width: 25%;
    }

    .main {
        padding: 26rpx;

        .qbsp {
            margin-bottom: 30rpx;
            font-size: 32rpx;
            color: #ffffff;
        }

        .shoplist {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30rpx;

            /* 允许换行 */
            .shopone {
                padding: 20rpx 30rpx;
                border-radius: 40rpx;
                background: #31405c;
                border: 2rpx solid #1a2b4a;

                .img {
                    width: 100%;
                    height: 176rpx;
                }

                .title {
                    margin-top: 14rpx;
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .txt {
                    margin-top: 10rpx;
                    font-family: Source Han Sans;
                    font-size: 24rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 100%;
                }

                .ppat {
                    margin-top: 20rpx;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;

                    .price {
                        font-family: Source Han Sans;
                        font-size: 20rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #ffffff;
                    }
                }
            }
        }
    }
}

.searchbox {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 20rpx;
}

.indicator {
    @include flex(row);
    justify-content: center;
    padding: 8rpx;
    border-radius: 120rpx;
    background: rgba(255, 255, 255, 0.2392);
    position: relative;

    &__dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background-color: #cacaca;
        margin: 0 10rpx;
        transition: background-color 0.3s;

        &--active {
            background-color: #ffffff;
        }
    }
}
</style>
