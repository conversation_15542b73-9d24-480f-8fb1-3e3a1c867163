<script setup lang="ts">
import { ref } from 'vue'

const dzlist = ref([
    {
        id: 1,
        name: '张xx',
        number: '15369875486',
        address: '内蒙古自治区呼和浩特市xxxxxxxxx',
        isme: true
    },
    {
        id: 1,
        name: '张xx',
        number: '15369875486',
        address: '内蒙古自治区呼和浩特市xxxxxxxxx'
    }
])
</script>
<template>
    <view class="information">
        <ex-header title="选择地址" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <view class="adlist">
                <view class="adone" v-for="(item, index) in dzlist" :key="index">
                    <view class="top">
                        <text class="name">{{ item.name }} {{ item.number }}</text>
                        <image src="@/static/xg.png" mode=""></image>
                    </view>
                    <view class="bottom">
                        <text class="mrdz" v-if="item.isme">默认地址</text>
                        <text class="xxdz">{{ item.address }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.information {
    .content {
        padding: 0 30rpx;
        box-sizing: border-box;
        .adlist {
            background: rgb(117 113 113 / 15%);
            border-radius: 10px;
            padding: 10px 20px;
            box-sizing: border-box;
            :last-child {
                border-bottom: 0 !important;
            }
            .adone {
                width: 100%;
                border-bottom: 1px solid #30e3ca61;
                display: grid;
                padding-bottom: 15px;
                margin-top: 20px;
                .top {
                    margin-left: 1%;
                    width: 99%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    .name {
                        font-family: Source Han Sans;
                        font-size: 16px;
                        color: #ffffff;
                    }
                    image {
                        width: 20px;
                        height: 20px;
                    }
                }
                .bottom {
                    margin-top: 10px;
                    margin-left: 1%;
                    width: 99%;
                    display: flex;
                    align-items: center;
                    .mrdz {
                        font-family: Source Han Sans;
                        font-size: 14px;
                        color: #0094ff;
                        margin-right: 10px;
                    }
                    .xxdz {
                        font-family: Source Han Sans;
                        font-size: 14px;
                        color: #ffffff;
                    }
                }
            }
        }
    }
}
</style>
