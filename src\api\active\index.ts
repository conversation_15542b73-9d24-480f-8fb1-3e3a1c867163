import { request } from '@/config/request'

//活动分类
export function activitycategory() {
    return request.http({
        url: 'api/shop/activity/category',
        method: 'GET'
    })
}

//活动列表
export function activityList(data: any) {
    return request.http({
        url: 'api/shop/activity/list',
        method: 'GET',
        data
    })
}

//活动详情
export function activityDetail(id: string) {
    return request.http({
        url: 'api/shop/activity/detail/' + id,
        method: 'GET'
    })
}

//活动报名
export function activitySign(data: any) {
    return request.http({
        url: 'api/shop/activity/sign',
        method: 'POST',
        data
    })
}

//我的活动列表
export function mySign(data: any) {
    return request.http({
        url: 'api/shop/activity/my_sign',
        method: 'POST',
        data
    })
}

//活动报名详情
export function signDetail(id: string) {
    return request.http({
        url: 'api/shop/activity/sign_detail/' + id,
        method: 'POST'
    })
}

//取消报名活动
export function cancelSign(data: any) {
    return request.http({
        url: 'api/shop/activity/cancel_sign',
        method: 'POST',
        data
    })
}

//签到核验
export function signIn(data: any) {
    return request.http({
        url: 'api/shop/activity/sign_in',
        method: 'POST',
        data
    })
}

//签退核验
export function signOut(data: any) {
    return request.http({
        url: 'api/shop/activity/sign_out',
        method: 'POST',
        data
    })
}

//活动用户协议
export function contentProtocol() {
    return request.http({
        url: 'api/shop/content/protocol',
        method: 'GET'
    })
}
