import { request } from '@/config/request'

//banner列表
export function bannerList(limit: string) {
    return request.http({
        url: 'api/shop/banner_list?limit=' + limit,
        method: 'GET'
    })
}

//banner详情
export function bannerDetail(id: string) {
    return request.http({
        url: 'api/shop/banner/detail/' + id,
        method: 'GET'
    })
}

//资讯文章/通知公告列表
export function article(data: any) {
    return request.http({
        url: 'api/shop/article',
        method: 'GET',
        data
    })
}

//资讯文章/通知公告详情
export function articleDetail(id: string) {
    return request.http({
        url: 'api/shop/article/detail/' + id,
        method: 'GET'
    })
}
