<template>
    <view class="classbox">
        <ex-header title="机构详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="headpart">
            <view class="head">
                <!-- <image class="tx" :src="getImageUrl(userdata.image_path)" mode="" /> -->
                <view class="flex column left_box">
                    <headerImage
                        :src="getHeaderImage(userdata.image_path)"
                        width="160rpx"
                        height="160rpx"
                        :is-icon="true"
                        style="border-radius: 50%; overflow: hidden"
                    />
                    <view class="rz flex-center-start">
                        <image class="icon" src="@/static/jlrz.png" mode=""></image>
                        LNT机构认证
                    </view>
                </view>
                <view class="flex1 namepa">
                    <view class="name">{{ userdata.title }}</view>
                    <view class="intr">{{ userdata.content }}</view>
                </view>
            </view>
        </view>
        <view class="mainpart">
            <text class="titletxt">机构介绍</text>
            <view class="bgbox">
                <view class="txtpart">
                    <text class="txt">机构名称：{{ userdata.title }}</text>
                    <text class="txt">机构介绍：{{ userdata.content }}</text>
                    <text class="txt">联系人：{{ userdata.name }}</text>
                    <text class="txt">联系电话：{{ userdata.mobile }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { getHeaderImage } from '@/utils/common'
import { organizationdetail } from '@/api/institution/index'

const userdata = ref<any>({})

onLoad(async (e) => {
    await organizationdetail(e.id).then((res: any) => {
        userdata.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    .headpart {
        width: 100%;
        padding: 40rpx;
        background-image: url('http://*************:8081//upload/attachment/image/202506/12/1749722009da680483b038d1663a564e3573a13dd6_local.png');
        background-size: 100% 100%;
        box-sizing: border-box;

        .head {
            margin-top: 96rpx;
            display: flex;
            align-items: center;
            align-items: end;

            .tx {
                margin-top: -6px;
                border-radius: 50%;
                width: 140rpx;
                height: 140rpx;
            }

            .namepa {
                margin-left: 26rpx;

                .name {
                    font-size: 36rpx;
                    color: #ffffff;
                }

                .intr {
                    margin-top: 20rpx;
                    font-size: 28rpx;
                    color: #ffffff;
                }
            }

            .rz {
                margin-top: 20rpx;
                padding: 7rpx 14rpx;
                border-radius: 14rpx;
                background: #25a3dd;
                font-size: 20rpx;
                color: #ffffff;

                .icon {
                    margin-right: 6rpx;
                    width: 30rpx;
                    height: 30rpx;
                }

                font-family: Source Han Sans;
                font-size: 20rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }

        .cardlist {
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            gap: 20rpx;

            .card {
                white-space: nowrap;
                padding: 6rpx 10rpx;
                border-radius: 86rpx;
                background: #00204a;
                box-sizing: border-box;
                border: 2rpx solid #00204a;
                display: flex;
                align-items: center;
                font-family: Source Han Sans;
                font-size: 26rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #efb900;

                .icom {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 6rpx;
                }
            }
        }
    }

    .mainpart {
        padding: 30rpx;

        .titletxt {
            margin-top: 40rpx;
            font-size: 28rpx;
            color: #ffffff;
        }

        .bgbox {
            width: 100%;
            margin-top: 20rpx;
            padding: 34rpx 48rpx;
            border-radius: 20rpx;
            background: rgba(242, 242, 242, 0.1961);
            display: flex;
            align-items: center;

            .tximg {
                width: 160rpx;
                height: 160rpx;
            }

            .txtpart {
                width: 100%;
                display: grid;
                align-items: center;
                gap: 10rpx;

                .txt {
                    max-width: 100%;
                    overflow-x: hidden;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }
        }

        .zsbox {
            margin-top: 20rpx;
            width: 100%;
            padding: 34rpx 48rpx;
            border-radius: 20rpx;
            background: rgba(242, 242, 242, 0.1961);
            display: grid;

            .titl {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .zsimg {
                margin-top: 20rpx;
                width: 100%;
                height: 480rpx;
            }
        }
    }
}
</style>
