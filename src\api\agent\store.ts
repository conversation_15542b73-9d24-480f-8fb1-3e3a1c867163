import { request } from '@/config/request'

export interface listType {
    page: number
    limit: number
    industry?: number
    type?: number
    keyword: string
}

export interface addOrEditType {
    agent_user_id: number
    id?: number
    merchant_name: string
    industry_desc: string
    industry: number
    type: number
    region_desc: string
    province: number
    city: number
    town: number
    address: string
    telephone: string
    business_license_photo: string
    door_header_photo: string
    interior_photo: string
    activity_name: string
    activity_type: string
    activity_image: string
    activity_rule: string
    stock: number | null
    effective_time: number | null
    link?: string
}
export function getDirectSubordinate() {
    return request.http({
        url: '/api/d.store/getDirectSubordinate'
    })
}

export function list(data: listType) {
    return request.http({
        url: '/api/d.store/getList',
        data: Object.assign(data, { examine_status: '0,1,2' })
    })
}

export function myBusinessManagement(data: listType) {
    return request.http({
        url: '/api/d.store/myBusinessManagement',
        data: Object.assign(data, { examine_status: '0,1,2' })
    })
}

export function info(id: number) {
    return request.http({
        url: '/api/d.store/getDetail',
        data: { id }
    })
}

export function addOrEdit(data: any) {
    return request.http({
        url: '/api/d.store/addOrEdit',
        data
    })
}

export function recharge(data: { id: number; amount: number }) {
    return request.http({
        url: '/api/d.store/recharge',
        data
    })
}

export function verification(id: number) {
    return request.http({
        url: '/api/d.store/verification',
        data: { id }
    })
}

export interface queryType {
    limit: number
    page: number
    store_id: number
    status: string
    begin_time: number
    end_time: number
}

export function verificationRecords(data: queryType) {
    return request.http({
        url: '/api/d.store/verificationRecords',
        data
    })
}
