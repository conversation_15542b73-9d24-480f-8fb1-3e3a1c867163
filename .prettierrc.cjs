module.exports = {
    //在单独的箭头函数参数周围包括括号 always：(x) => x \ avoid：x => x
    arrowParens: 'always',
    // 开始标签的右尖括号是否跟随在最后一行属性末尾，默认false
    bracketSameLine: false,
    // 对象字面量的括号之间打印空格 (true - Example: { foo: bar } ; false - Example: {foo:bar})
    bracketSpacing: true,
    // 是否格式化一些文件中被嵌入的代码片段的风格(auto|off;默认auto)
    embeddedLanguageFormatting: 'auto',
    // 指定 HTML 文件的空格敏感度 (css|strict|ignore;默认css)
    htmlWhitespaceSensitivity: 'ignore',
    // 一行最多多少个字符
    printWidth: 150,
    // 超出打印宽度 (always | never | preserve )
    proseWrap: 'preserve',
    // 对象属性是否使用引号(as-needed | consistent | preserve;默认as-needed:对象的属性需要加引号才添加;)
    quoteProps: 'as-needed',
    // 指定要使用的解析器，不需要写文件开头的 @prettier
    requirePragma: false,
    // 不需要自动在文件开头插入 @prettier
    insertPragma: false,
    // 最后不需要引号
    semi: false,
    // 使用单引号 (true:单引号;false:双引号)
    singleQuote: true,
    // 缩进空格数，默认2个空格
    tabWidth: 4,
    // 多行时尽可能打印尾随逗号。（例如，单行数组永远不会出现逗号结尾。） 可选值"<none|es5|all>"，默认none
    trailingComma: 'none',
    // 使用制表符而不是空格缩进行
    useTabs: false,
    // Vue文件脚本和样式标签缩进
    vueIndentScriptAndStyle: false,
    // 换行符使用 lf 结尾是 可选值"<auto|lf|crlf|cr>"
    endOfLine: 'auto'
}