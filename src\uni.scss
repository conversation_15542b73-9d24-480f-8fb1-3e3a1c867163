@import 'uview-plus/theme.scss';
// @use 'uview-plus/theme.scss';

#app {
    box-sizing: border-box;
}

page {
    background-color: #1a2b4a;
}

/* 颜色变量 */

/*============================= 文字尺寸 =============================*/
$font-size-20: 20rpx;
$font-size-22: 22rpx;
$font-size-24: 24rpx;
$font-size-26: 26rpx;
$font-size-28: 28rpx;
$font-size-30: 30rpx;
$font-size-32: 32rpx;
$font-size-34: 34rpx;
$font-size-36: 36rpx;
$font-size-40: 40rpx;

image {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

view {
    box-sizing: border-box;
}

/*============================= 弹性盒子 =============================*/
%flex-base {
    display: flex;
    flex-wrap: nowrap;
}

$flex-way: (
    start,
    flex-start),
    (center, center),
    (end, flex-end),
    (between, space-between),
    (around, space-around),
    (evenly, space-evenly
);

@mixin flex-algin($align) {
    @each $way, $justify in $flex-way {
        &-#{$way} {
            @if $way !=$align {
                @if $way !=$align {
                    @extend %flex-base;
                    align-items: $align;
                    justify-content: $justify;
                }
            }
        }
    }
}

.flex {
    @extend %flex-base;
    align-items: center;
    justify-content: center;

    @each $way, $justify in (start, flex-start), (center, center), (end, flex-end) {
        &-#{$way} {
            @include flex-algin($justify);
        }
    }
}

[class*="flex-"],
[class^="flex"] {
    &.nowrap {
        flex-wrap: nowrap;
    }

    &.stretch {
        align-items: stretch;
    }

    @each $direction, $name in (row, direction), (column, direction), (wrap, wrap) {
        &.#{$direction} {
            flex-#{$name}: $direction;

            &-reverse {
                flex-#{$name}: '#{$direction}-reverse';
            }
        }
    }
}

@for $i from 1 through 4 {
    .flex#{$i} {
        flex: $i;
    }
}

/*============================= 文字溢出 =============================*/
.text-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    @for $i from 2 through 4 {
        &-#{$i} {
            overflow: hidden;
            white-space: normal;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            line-clamp: 1;
            -webkit-line-clamp: $i;
        }
    }
}

button::after {
    border: none;
}

button {
    position: relative;
    display: block;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0px;
    padding-right: 0px;
    box-sizing: border-box;
    text-align: center;
    text-decoration: none;
    line-height: 1.35;
    -webkit-tap-highlight-color: transparent;
    overflow: hidden;
    color: #000;
    font-size: 32rpx;
    background-color: inherit;
    width: 100%;
    height: 100%;
}