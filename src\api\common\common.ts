import { request } from '@/config/request'
import useUserStore from '@/store/user'

export interface sendCodeType {
    phone: number | string
}

export function payInfo(type: string, id: number) {
    return request.http({
        url: `api/pay/info/${type}/${id}`,
        method: 'GET'
    })
}

export function userPay(data: { type: string; trade_id: number; trade_type: string }) {
    return request.http({
        url: 'api/pay',
        data
    })
}

export function roleInfo() {
    return request.http({
        url: 'api/member/role',
        method: 'GET'
    })
}

// 上传图片
export function uploadFile(file: any) {
    let url = ''
    const token = useUserStore().token
    // #ifdef APP-PLUS
    url = import.meta.env.VITE_APP_BASE_URL
    // #endif
    // #ifdef H5 || MP-WEIXIN
    url = import.meta.env.VITE_APP_BASE_URL
    // #endif
    console.log('file', file)
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: `${url}api/file/image`,
            filePath: file,
            name: 'file',
            header: { token },
            success: (res) => {
                resolve(JSON.parse(res.data))
            },
            fail: (res) => {
                reject(res)
            }
        })
    })
}
