import { request } from '@/config/request'

export function getOpenId(data: { code: string }) {
    return request.http({
        url: 'api/weapp/get_openId',
        data
    })
}

export function login(data: { code: string; openid: string; pid?: string }) {
    return request.http({
        url: 'api/weapp/mobile_auth',
        data
    })
}

export function getUserInfos() {
    return request.http({
        url: 'api/member/member',
        method: 'GET'
    })
}

export function getMemberLevel() {
    return request.http({
        url: 'api/member/level',
        method: 'GET'
    })
}

export function getRegion() {
    return request.http({
        url: 'api/region',
        method: 'GET'
    })
}

export function agreement() {
    return request.http({
        url: 'api/agreement',
        method: 'GET'
    })
}

export function phoneLogin(data: any) {
    return request.http({
        url: 'api/login',
        method: 'GET',
        data
    })
}
