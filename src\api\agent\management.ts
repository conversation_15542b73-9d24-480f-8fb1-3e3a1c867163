import { request } from '@/config/request'

export function overview(type: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.income/${type === 'pos' ? 'overview' : 'learnOverview'}`
    })
}

interface rewardsType extends pageType {
    type?: string
    begin_time?: number
    end_time?: number
    stage?: string
}

export function rewards(data: rewardsType, type: 'recommend' | 'phase' | 'accompany' | 'profit' | 'commission' | 'cRoyalties' | 'cRecommend') {
    const urls: { [n: string]: string } = {
        recommend: 'recommendationReward',
        phase: 'stageReward',
        accompany: 'accompanyingReward',
        profit: 'dividendIncome',
        commission: 'commission',
        cRoyalties: 'cRoyalties',
        cRecommend: 'cRecommend'
    }

    return request.http({
        url: `/api/d.income/${urls[type]}`,
        data
    })
}

interface getInfoType extends pageType {
    type?: string
    begin_time?: number
    end_time?: number
}
export function getInfo(data: getInfoType, type: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.income/${type === 'pos' ? 'getinfo' : 'getlearninfo'}`,
        data
    })
}

export function getCount(data: getInfoType, type: 'pos' | 'learn') {
    return request.http({
        url: `/api/d.income/${type === 'pos' ? 'getCount' : 'getlearninfo'}`,
        data
    })
}

// 分红奖励
export function dividendsReward(data: { uid: number }) {
    return request.http({
        url: '/api/d.agentuserbonus/dividends',
        data
    })
}

// 股东会大盘分红
export function myShareholderOverallDividends() {
    return request.http({
        url: '/api/d.agentuserbonus/myShareholderOverallDividends'
    })
}

// 我的分红
export function myDividends(data: { year: string; month: string }) {
    return request.http({
        url: '/api/d.agentuserbonus/myDividends',
        data
    })
}
export function getinfoApi(data: { search?: string; tab: number }) {
    return request.http({
        url: '/api/d.agentuserbonus/getInfo',
        data
    })
}
