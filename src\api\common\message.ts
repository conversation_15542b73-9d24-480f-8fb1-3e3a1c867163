import { request } from '@/config/request'

export type typeType = 'personal' | 'system'

export function getMessageCount(scope: useType) {
    return request.http({
        url: '/api/t.noticePersonal/getUnreadedNum',
        data: { scope }
    })
}

export function getMessageList(data: { page: number; limit: number; type: string; scope: useType }, type: typeType) {
    return request.http({
        url: `/api/t.${type === 'personal' ? 'noticePersonal' : 'noticeSystem'}/getList`,
        data
    })
}

export function getMessageRead(id: number, scope: useType, type: typeType) {
    return request.http({
        url: `/api/t.${type === 'personal' ? 'noticePersonal' : 'noticeSystem'}/markReaded`,
        data: { id, scope }
    })
}

export function getMessageDetail(id: number) {
    return request.http({
        url: `/api/t.noticeSystem/getDetail`,
        data: { id }
    })
}

export function getDanmakuList(data: { page: number; limit: number; type?: string; scope: useType }) {
    return request.http({
        url: `/api/t.noticeDanmaku/getList`,
        data
    })
}
