<script setup lang="ts">
import informationItem from '@/components/information-item/information-item.vue'
import { myCollect, removeCollect } from '@/api/mine/user'
import { onShareAppMessage, onShareTimeline, onShow } from '@dcloudio/uni-app'
import { useShare } from '@/composables/useShare'

// 使用全局分享功能 - 收藏页面跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: 'LNT(中国) - 我的收藏资讯分享',
    imageUrl: '/static/logo.png',
    path: '/subPages/Information/index',
    redirectToHome: true // 收藏页面分享后跳转到首页
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})
const collectList = ref<{ articleInfo: { [n: string]: string | number } }[]>([])
const loading = ref(false)
const error = ref('')
const loadCollectList = async () => {
    try {
        loading.value = true
        const response = await myCollect()
        collectList.value = response.data
    } catch (err) {
        error.value = '加载失败，请重试'
        console.error('加载收藏列表失败:', err)
    } finally {
        loading.value = false
    }
}
const cancelCollect = async ({ id }: { id: number }) => {
    try {
        await removeCollect({ item_id: id })
        collectList.value = collectList.value.filter((v) => v.articleInfo.id !== id)
        uni.showToast({ title: '取消收藏成功', icon: 'none', mask: true })
    } catch (err) {
        uni.showToast({ title: '取消收藏失败', icon: 'none', mask: true })
        console.error('取消收藏失败:', err)
    }
}

onShow(async () => {
    // 处理分享参数
    await handleShareParams()
})

onMounted(() => {
    loadCollectList()
})
</script>
<template>
    <view class="information">
        <ex-header title="我收藏的资讯" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <template v-if="collectList.length > 0">
                <block v-for="(v, k) in collectList" :key="k" class="item">
                    <informationItem :item="v.articleInfo" :is-show-tools="true" @cancelCollect="cancelCollect" />
                </block>
            </template>
            <ex-empty v-else tips="暂无收藏~" />
        </view>
    </view>
</template>

<style scoped lang="scss">
.information {
    .content {
        padding: 30rpx;
    }
}
</style>
