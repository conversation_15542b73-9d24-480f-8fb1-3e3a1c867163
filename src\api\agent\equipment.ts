import { request } from '@/config/request'

export interface queryType extends pageType {
    status: string
}

export function getLeftList(version: string) {
    return request.http({
        url: '/api/d.policy/posPolicyList',
        data: { version }
    })
}

export function getPolicysList() {
    return request.http({
        url: '/api/d.policylearning/policyList'
    })
}

// 兑换礼包 [学习机]
export function getStockVouchersNumber(data: { version: string }) {
    return request.http({
        url: '/api/d.terminalLearn/getStockVouchersNumber',
        data
    })
}

// 获取品牌[POS机 | 学习机]
export const brandListApi = (type: 'pos' | 'learn') => {
    return request.http({
        url: `/api/d.${type === 'learn' ? 'terminallearn/terminalList' : 'terminal/getBrand'}`
    })
}

// 获取兑换人员[POS机 | 学习机]
export const persionList = (data: { search: string }, type: 'pos' | 'learn') => {
    return request.http({
        url: `/api/d.${type === 'learn' ? 'terminallearn/terminalList' : 'partnerRedemption/persionList'}`,
        data
    })
}

// 获取兑换数据
export const GetBasicParameters = () => {
    return request.http({
        url: '/api/d.partnerRedemption/GetBasicParameters'
    })
}

// 机具订单
export const listApi = (data: { page: number; limit: number; status: string }) => {
    return request.http({
        url: '/api/d.exchangeTerminalOrder/list',
        data
    })
}

// 订单详情
export const detailApi = (id: number) => {
    return request.http({
        url: '/api/d.exchangeTerminalOrder/details',
        data: { id }
    })
}

export const getStockList = (data: { page: number; limit: number; version: string; status: string }) => {
    return request.http({
        url: '/api/d.exchangeVoucher/myList',
        data
    })
}

// 股金券[POS机 | 学习机]
export const stockList = () => {
    return request.http({
        url: '/api/d.exchangeVoucher/overviewAndDetails'
    })
}

// 股金券[POS机 | 学习机] NEW - 向平台兑换
export const newStockList = (data: { page: number; limit: number; version: string; first_round: number }) => {
    return request.http({
        url: '/api/d.exchangeVoucher/getList',
        data
    })
}

// 股金券[POS机 | 学习机] NEW - 向伙伴兑换
export const listOfCoupons = (data: { page: number; limit: number; version: string }) => {
    return request.http({
        url: '/api/d.partnerRedemption/listOfCoupons',
        data
    })
}

// 兑换下单[POS机 | 学习机] - 向伙伴兑换
export const createOrder = (data: { [n: string]: string }) => {
    return request.http({
        url: '/api/d.partnerRedemption/createOrder',
        data
    })
}

// 订单列表 - 向伙伴兑换`
export const applyforList = (data: pageType, status: boolean = false) => {
    return request.http({
        url: `/api/d.partnerRedemption/${!status ? 'orderList' : 'isRedeemedList'}`,
        data
    })
}

// 订单审核 - 向伙伴兑换
export const orderAudit = (data: { id: number; status: number }) => {
    return request.http({
        url: `/api/d.partnerRedemption/audit`,
        data
    })
}

// 兑换下单[POS机 | 学习机]
export const payOrder = (
    data: {
        terminal_brand: string
        terminal_type: string
        terminal_deposit: string
        terminal_rate: string
        num: string
        delivery_type: string
        exchange_voucher_id_arr: string[]
        address_id?: string
    },
    type: 'pos' | 'learn'
) => {
    return request.http({
        url: `/api/d.${type === 'learn' ? 'exchangeTerminalLearnOrder' : 'exchangeTerminalOrder'}/createOrder`,
        data
    })
}
