import { request } from '@/config/request'

export const maintenance = () => {
    return request.http({
        url: '/api/d.agentMerchant/agentMaintenance'
    })
}

export const merchantManagement = (type: string) => {
    return request.http({
        url: '/api/d.agentMerchant/merchantManagement',
        data: { type }
    })
}

export const statusList = () => {
    return request.http({
        url: '/api/d.agentMerchant/merchant_management_listings'
    })
}

export const listApi = (data: { search: string }) => {
    return request.http({
        url: '/api/d.agentMerchant/managementList',
        data
    })
}

export const infoApi = (id: number) => {
    return request.http({
        url: '/api/d.agentMerchant/managementInfo',
        data: { id }
    })
}

export const agentMaintenanceList = (data: { search?: string; type?: string; level?: string; limit: number; page: number }) => {
    return request.http({
        url: '/api/d.agentMerchant/agentMaintenanceList',
        data
    })
}

export const agentMaintenanceInfo = (uid: number) => {
    return request.http({
        url: '/api/d.agentMerchant/agentMaintenanceInfo',
        data: { uid }
    })
}
