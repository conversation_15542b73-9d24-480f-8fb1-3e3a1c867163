<script setup lang="ts">
import { authenticationResult } from '@/api/mine/user'
import { getImageUrl } from '@/utils/common'

const doSearch = (formData: { page: number; limit: number; activity_status: string }, onSuccess: Function) => {
    const submitData = { ...formData }
    authenticationResult(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}

const previewImage = (path: string) => {
    uni.previewImage({
        urls: [getImageUrl(path)],
        showmenu: true
    })
}
</script>
<template>
    <view class="mine">
        <ex-header title="我的认证" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <ex-list ref="orderListRef" :on-form-search="doSearch">
                <template v-slot="{ row }">
                    <view class="item">
                        <view class="time">申请时间：{{ row.create_time }}</view>
                        <view class="items">
                            <view class="flex-start-start">
                                <view class="label">姓名</view>
                                <view class="desc">{{ row.name }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">性别</view>
                                <view class="desc">{{ row.sex_msg }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">手机号</view>
                                <view class="desc">{{ row.mobile }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">认证等级</view>
                                <view class="desc">{{ row.member_role_name }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">证书名称</view>
                                <view class="desc">{{ row.title || '暂无' }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">证书编号</view>
                                <view class="desc">{{ row.num || '暂无' }}</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">证书</view>
                                <view class="desc look" @tap="previewImage(row.image_path)">查看证书</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">照片</view>
                                <view class="desc look" @tap="previewImage(row.photo_path)">查看照片</view>
                            </view>
                            <view class="flex-start-start">
                                <view class="label">个人介绍</view>
                                <view class="desc">{{ row.introduce || '暂无' }}</view>
                            </view>
                            <view class="status">{{ row.status_msg }}</view>
                        </view>
                        <view class="status_time" v-if="row.status === 1">认证成功：{{ row.status_time }}生效</view>
                    </view>
                </template>
            </ex-list>
        </view>
    </view>
</template>

<style scoped lang="scss">
.mine {
    .content {
        padding: 30rpx;

        .item {
            margin-bottom: 30rpx;

            .time {
                font-size: 28rpx;
                color: #ffffff;
            }

            .items {
                margin: 20rpx 0;
                background-color: #2f3e5a;
                border-radius: 18rpx;
                padding: 30rpx;
                font-family: Source Han Sans;
                font-size: 28rpx;
                color: #ffffff;

                .flex-start-start {
                    margin-bottom: 20rpx;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .label {
                        min-width: 150rpx;
                    }

                    .desc {
                        word-break: break-all;
                    }

                    .look {
                        color: #25a3dd;
                    }
                }
            }

            .status {
                background-color: #1989fa;
                border-radius: 8rpx;
                padding: 10rpx 0;
                text-align: center;
            }

            .status_time {
                background-color: #8cc046;
                border-radius: 8rpx;
                padding: 10rpx 0;
                text-align: center;
                font-size: 26rpx;
                letter-spacing: normal;
                color: #ffffff;
            }
        }
    }
}
</style>
