import { request } from '@/config/request'

export function list(data: pageType) {
    return request.http({
        url: '/api/d.shareholderrating/shareholderList',
        data
    })
}

export function grading(data: { uid: number; id: string }) {
    return request.http({
        url: '/api/d.shareholderrating/grading',
        data
    })
}

export function gradingRecords(data: pageType) {
    return request.http({
        url: '/api/d.shareholderrating/gradingRecords',
        data
    })
}
