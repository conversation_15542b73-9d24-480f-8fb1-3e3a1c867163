import { request } from '@/config/request'

//商品列表
export function goodsPages(data: any) {
    return request.http({
        url: 'api/shop/goods/pages',
        method: 'GET',
        data
    })
}

//商品详情
export function goodsDetail(data: any) {
    return request.http({
        url: 'api/shop/goods/detail',
        method: 'GET',
        data
    })
}

//获取商品详情
export function goodsSku(id: string) {
    return request.http({
        url: 'api/shop/goods/sku/' + id,
        method: 'GET'
    })
}

//商品分类
export function categoryList() {
    return request.http({
        url: 'api/shop/goods/category/list',
        method: 'GET'
    })
}

//添加购物车
export function addCart(data: any) {
    return request.http({
        url: 'api/shop/cart',
        method: 'POST',
        data
    })
}

//编辑购物车
export function editCart(data: any) {
    return request.http({
        url: 'api/shop/cart',
        method: 'PUT',
        data
    })
}

//购物车删除
export function deleteCart(data: any) {
    return request.http({
        url: 'api/shop/cart/delete',
        method: 'PUT',
        data
    })
}

//清空购物车
export function clearCart() {
    return request.http({
        url: 'api/shop/cart/clear',
        method: 'DELETE'
    })
}

//购物车列表
export function cartList() {
    return request.http({
        url: 'api/shop/cart',
        method: 'GET'
    })
}

//购物车数量
export function cartSum() {
    return request.http({
        url: 'api/shop/cart/sum',
        method: 'GET'
    })
}

//购物车计算
export function cartCalculate() {
    return request.http({
        url: 'api/shop/cart/calculate',
        method: 'GET'
    })
}
