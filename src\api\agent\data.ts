import { request } from '@/config/request'

export type sortT = 'query' | 'shareholderCount' | 'businessesCount' | 'activationCount' | 'tradeCount' | 'earningsCount'

export interface dataType {
    tab: number
    day: number | null
    beginTime: number | null
    endTime: number | null
}

export const queryApi = (data: dataType, type: sortT) => {
    return request.http({
        url: `/api/d.agentUserCount/${type}`,
        data
    })
}

export const getDetail = (type: string) => {
    return request.http({
        url: '/api/d.agentUserCount/getDetail',
        data: { type }
    })
}
