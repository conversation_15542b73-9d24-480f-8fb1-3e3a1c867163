import { request } from '@/config/request'
import { userInfoInt, coachInfoInt, certSubInt, certificationInt } from './type'

export function updateUser(data: { data: userInfoInt }) {
    return request.http({
        url: 'api/member/edit',
        method: 'PUT',
        data
    })
}

export function courseOrder(data: { status: number; page: number; limit: number }) {
    return request.http({
        url: 'api/shop/course/order/list',
        data
    })
}

export function authentication(data: coachInfoInt) {
    return request.http({
        url: 'api/shop/coach/authentication',
        data
    })
}

export function authenticationResult(data: { page: number; limit: number }) {
    return request.http({
        url: 'api/shop/coach/authentication_result',
        method: 'GET',
        data
    })
}

export function about() {
    return request.http({
        url: 'api/shop/content/about',
        method: 'GET'
    })
}

export function myCert(data: { page: number; limit: number }) {
    return request.http({
        url: 'api/shop/certificate/my_cert_list',
        method: 'GET',
        data
    })
}

export function myCertSub(data: certSubInt) {
    return request.http({
        url: 'api/shop/certificate/my_cert_sub',
        data
    })
}

// 我的收藏
export function myCollect() {
    return request.http({
        url: 'api/shop/article/my_collect',
        method: 'GET'
    })
}

// 收藏详情
export function collectDet(id: number) {
    return request.http({
        url: `api/shop/article/detail/${id}`,
        method: 'GET'
    })
}

// 取消收藏
export function removeCollect(data: { item_id: number }) {
    return request.http({
        url: 'api/shop/article/collect_remove',
        data
    })
}

// 收藏资讯
export function collect(data: { item_id: number }) {
    return request.http({
        url: 'api/shop/article/collect',
        data
    })
}

// 是否收藏
export function isCollect(data: { item_id: number }) {
    return request.http({
        url: 'api/shop/article/is_collect',
        data
    })
}

// 完善资料(认证)
export function certification(data: certificationInt) {
    return request.http({
        url: 'api/member/certification',
        data
    })
}

// 完善资料(认证)
export function upMobile(data: { mobile: string }) {
    return request.http({
        url: 'api/member/update_mobile',
        data
    })
}

// 扫码签到
export function signIn(data: { verification_code: string }) {
    return request.http({
        url: 'api/shop/activity/sign_in',
        data
    })
}

// 扫码签退
export function signOut(data: { verification_code: string }) {
    return request.http({
        url: 'api/shop/activity/sign_out',
        data
    })
}
