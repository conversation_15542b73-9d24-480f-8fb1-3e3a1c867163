<template>
    <u-navbar
        title="教练详情"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <view class="headpart">
            <view class="flex-start-start head">
                <image class="tx" :src="getHeaderImage(userdata.photo)" mode="aspectFill" />
                <view class="namepa flex1">
                    <view class="flex-start-between">
                        <text class="name">{{ userdata.name }}</text>
                        <view class="name_box">
                            <view class="rz">
                                <image class="icon" src="@/static/jlrz.png" mode="" />
                                LNT教练认证
                            </view>
                        </view>
                    </view>
                    <text class="intr">{{ userdata.introduction }}</text>
                </view>
            </view>
            <view class="cardlist">
                <view class="card">
                    <image class="icom" src="@/static/hy.png" mode="" />
                    {{ userdata.member_role_name }}
                </view>
                <view class="card">
                    <image class="icom" src="@/static/jrlnt.png" mode="" />
                    {{ convertTimestamp(userdata.coach_time).slice(0, 10) }}加入LNT（中国）
                </view>
            </view>
        </view>
        <view class="mainpart">
            <text class="titletxt">教练介绍</text>
            <view class="flex-start-start bgbox">
                <image class="tximg" :src="getHeaderImage(userdata.photo)" mode="aspectFill" />
                <view class="txtpart flex1">
                    <text class="txt">姓名：{{ userdata.name }}</text>
                    <text class="txt">电话：{{ userdata.mobile }}</text>
                    <text class="txt">介绍：{{ userdata.introduction }}</text>
                </view>
            </view>
            <text class="titletxt">证书展示</text>
            <view class="zsbox" v-for="(item, index) in zsdata" :key="index">
                <text class="titl">{{ item.member_role_name }}</text>
                <image class="zsimg" :src="getImageUrl(item.certificate_path)" mode="" />
            </view>
        </view>
    </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app'
import { coachDetail, certList } from '@/api/coach/index'
import { getHeaderImage, convertTimestamp, getImageUrl } from '@/utils/common'
import { ref } from 'vue'

const userdata = ref({})
const zsdata = ref([])

onLoad(async (e) => {
    await coachDetail(e.id).then((res: any) => {
        console.log(res)
        userdata.value = res.data
    })
    await certList(e.id).then((res: any) => {
        console.log(res)
        zsdata.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    display: grid;
    align-content: flex-start;
    box-sizing: border-box;

    .headpart {
        width: 100%;
        padding: 40rpx;
        background-image: url('http://*************:8081//upload/attachment/image/202506/12/1749722009da680483b038d1663a564e3573a13dd6_local.png');
        background-size: 100% 100%;

        .head {
            margin-top: 96rpx;
            display: flex;
            align-items: center;
            align-items: end;

            .tx {
                width: 140rpx;
                height: 140rpx;
            }

            .namepa {
                margin-left: 26rpx;
                display: grid;

                .name {
                    font-family: Source Han Sans;
                    font-size: 36rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }

                .intr {
                    margin-top: 28rpx;
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }

            .rz {
                margin-left: 50rpx;
                display: flex;
                align-items: center;
                border-radius: 14rpx;
                background: #8cc046;
                padding: 6rpx 10rpx;

                .icon {
                    margin-right: 6rpx;
                    width: 30rpx;
                    height: 30rpx;
                }

                font-family: Source Han Sans;
                font-size: 20rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }

        .cardlist {
            margin-top: 20rpx;
            display: flex;
            align-items: center;
            gap: 20rpx;

            .card {
                white-space: nowrap;
                padding: 6rpx 10rpx;
                border-radius: 86rpx;
                background: #00204a;
                box-sizing: border-box;
                border: 2rpx solid #00204a;
                display: flex;
                align-items: center;
                font-family: Source Han Sans;
                font-size: 26rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #efb900;

                .icom {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 6rpx;
                }
            }
        }
    }

    .mainpart {
        width: 100%;
        box-sizing: border-box;
        padding: 0 30rpx 20rpx 30rpx;
        display: grid;

        .titletxt {
            margin-top: 40rpx;
            font-family: Source Han Sans;
            font-size: 28rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .bgbox {
            margin-top: 20rpx;
            width: 100%;
            padding: 34rpx 48rpx;
            border-radius: 20rpx;
            background: rgba(242, 242, 242, 0.1961);

            .tximg {
                width: 160rpx;
                height: 160rpx;
            }

            .txtpart {
                margin-left: 64rpx;
                display: grid;
                align-items: center;
                gap: 10rpx;

                .txt {
                    font-family: Source Han Sans;
                    font-size: 28rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }
        }

        .zsbox {
            margin-top: 20rpx;
            width: 100%;
            padding: 34rpx 48rpx;
            border-radius: 20rpx;
            background: rgba(242, 242, 242, 0.1961);
            display: grid;

            .titl {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .zsimg {
                margin-top: 20rpx;
                width: 100%;
                height: 480rpx;
            }
        }
    }
}
</style>
