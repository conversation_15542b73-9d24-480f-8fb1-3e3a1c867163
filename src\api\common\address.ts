import { request } from '@/config/request'

const userPrefix = '/api/y.clientAddress'
const agentPrefix = '/api/d.agentAddress'

export interface listType {
    address?: string
    city?: number
    id?: number
    is_default?: number
    name?: string
    phone?: string
    province?: number
    town?: number
    region_desc?: string
}

export interface updateType {
    id?: number | undefined
    name: string
    phone: string
    province: number
    city: number
    town: number
    address: string
    region_desc: string
}

export function list(data: pageType, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/list`,
        data
    })
}

export function info(id: number, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/addressDetails`,
        data: { id }
    })
}

export function update(data: updateType, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/addOrUpdate`,
        data
    })
}

export function del(ids: string, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/destroy`,
        data: { ids }
    })
}

export function setDefault(id: number, type: useType) {
    return request.http({
        url: `${type === 'client' ? userPrefix : agentPrefix}/setDefault`,
        data: { id }
    })
}
