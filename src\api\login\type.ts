// 登录
export interface userInfoInt {
    account: string
    password: string
}

// 注册
export interface registerType {
    phone: string
    password: string
    code: string
    resetpassword: string
    superior_phone: string
}

// 忘记密码
export interface forgetPassword {
    account: string
    code: string
    password: string
    resetpassword: string
}

// 修改用户信息
export interface editUserInfoType {
    head_image?: string
    nickname?: string
}

// 修改支付密码
export interface payPasswordType {
    account: string
    pay_password: string
    code: string
}
