{
    "extends": "@vue/tsconfig/tsconfig.json",
    "compilerOptions": {
        "sourceMap": true,
        "baseUrl": ".",
        "paths": {
            "@/*": [
                "./src/*"
            ]
        },
        "lib": [
            "esnext",
            "dom"
        ],
        "types": [
            "vite/client",
            "@dcloudio/types",
        ],
        "typeRoots": [
            "src/**/*.d.ts"
        ]
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "src/types/*.d.ts"
    ]
}