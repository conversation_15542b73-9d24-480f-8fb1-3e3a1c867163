{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:prop": "uni --mode=production", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "add": "node ./auto/addPage.ts", "preinstall": "node ./auto/preinstall.js", "lint": "eslint --ext .ts,.js,.vue ./src", "fix": "eslint --fix --ext .ts,.js,.vue ./src", "prepare": "husky install", "rm": "rm -rf node_modules package-lock.json pnpm-lock.yaml && pnpm install"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-app-plus": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-components": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-h5": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-3081220230802001", "@qiun/ucharts": "2.5.0-20230101", "animate.css": "^4.1.1", "echarts": "^5.5.0", "pinia": "2.0.36", "sass": "1.63.2", "sass-loader": "10.4.1", "uview-plus": "^3.1.32", "vue": "^3.2.45", "vue-i18n": "^9.1.9"}, "devDependencies": {"@babel/eslint-parser": "^7.22.9", "@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-3081220230802001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-3081220230802001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-3081220230802001", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vue/tsconfig": "^0.1.3", "@vueuse/core": "^10.3.0", "eslint": "^8.46.0", "eslint-config-prettier": "^8.9.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.16.1", "feng-uniapp-exploit": "^1.0.2", "husky": "^8.0.0", "pinia-plugin-unistorage": "^0.0.17", "prettier": "^3.0.0", "sass-loader": "^10.4.1", "typescript": "^4.9.4", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "4.0.3", "vue-tsc": "^1.0.24"}}