import { request } from '@/config/request'
import { courseInt } from './type'

// 课程分类
export function getCourseCategory() {
    return request.http({
        url: 'api/shop/course/category',
        method: 'GET'
    })
}

//课程列表
export function getCourse(data: courseInt) {
    return request.http({
        url: 'api/shop/course',
        method: 'GET',
        data
    })
}

//热门课程（首页）
export function courseIndex() {
    return request.http({
        url: 'api/shop/course_index',
        method: 'GET'
    })
}

//课程详情
export function getCourseDetail(id: number) {
    return request.http({
        url: `api/shop/course/${id}`,
        method: 'GET'
    })
}

export function createOrder(data: { course_id: number }) {
    return request.http({
        url: 'api/shop/course/order/create',
        data
    })
}

export function orderDetail(order_id: number) {
    return request.http({
        url: `api/shop/course/order/detail/${order_id}`
    })
}

// 课程退款
export function courseRefund(data: { order_id: number }) {
    return request.http({
        url: 'api/shop/course/order/refund',
        data
    })
}
