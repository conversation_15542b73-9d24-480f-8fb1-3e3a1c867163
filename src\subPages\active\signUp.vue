<template>
    <view class="classbox">
        <ex-header title="报名详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="card">
            <view class="imgcard">
                <image class="pic" :src="getImageUrl(activeData.pic_path)" mode=""></image>
                <text class="title">{{ activeData.title }}</text>
            </view>
        </view>
        <view class="card">
            <text class="headtxt">活动详情</text>
            <text class="time">活动时间：{{ activeData.activity_start_time }}~{{ activeData.activity_end_time }}</text>
            <text class="time">活动地点：{{ activeData.address || '暂无' }}</text>
            <text class="time" v-if="activeData.is_price === 1">报名费用：{{ activeData.price }}</text>
        </view>
        <view class="card">
            <text class="headtxt">完善报名信息</text>
            <text class="time">手机号码 {{ userStore.userInfo.mobile }}</text>
            <view class="namepart">
                <text class="name">姓名</text>
                <input class="inputt" type="text" v-model="param.name" />
            </view>
        </view>
    </view>
    <view class="bottom">
        <view class="but" @click="submit">提交</view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { activityDetail, activitySign } from '@/api/active/index'
import { getImageUrl } from '@/utils/common'
import { payInfo, userPay } from '@/api/common/common'
import useUserStore from '@/store/user'
const userStore = useUserStore()

const activeData = ref({})
const param = ref({
    name: userStore.userInfo.name,
    activity_id: ''
})

const submit = () => {
    activitySign(param.value).then((res: any) => {
        if (res.code === 1) {
            if(res.data.is_price === 1) {
				userPay({ type: 'wechatpay', trade_id: res.data.order_id, trade_type: res.data.trade_type }).then((res) => {
				    const { data: payData } = res as { data: { [n: string]: string } }
				    uni.requestPayment({
				        provider: 'wxpay',
				        timeStamp: payData.timeStamp,
				        nonceStr: payData.nonceStr,
				        package: payData.package,
				        signType: payData.signType,
				        paySign: payData.paySign,
				        success: function (res: any) {
				            console.log('success:' + JSON.stringify(res))
				            if (res.errMsg === 'requestPayment:ok') {
				                uni.navigateBack()
				            }
				        },
				        fail: function (err: any) {
				            console.log('fail:' + JSON.stringify(err))
				        }
				    } as any)
				})
			} else {
				uni.navigateBack()
				uni.showToast({
					title: '报名成功！'
				})
			}
        }
    })
}

onLoad(async (e: any) => {
    param.value.activity_id = e.id
    await activityDetail(e.id).then((res: any) => {
        activeData.value = res.data
    })
})
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx;

    .card {
        margin-bottom: 30rpx;
        width: 100%;
        border-radius: 20rpx;
        background: rgba(188, 188, 188, 0.1);
        padding: 20rpx 30rpx;
        display: grid;

        .imgcard {
            display: flex;

            .pic {
                width: 190rpx;
                height: 130rpx;
                border-radius: 20rpx;
            }

            .title {
                flex: 1;
                width: 0;
                margin-left: 46rpx;
                font-family: Source Han Sans;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }

        .headtxt {
            font-family: Source Han Sans;
            font-size: 32rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .time {
            margin-top: 16rpx;
            font-family: Source Han Sans;
            font-size: 28rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .namepart {
            margin-top: 16rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .name {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .inputt {
                width: 80%;
                height: 56rpx;
                border-radius: 10rpx;
                background: rgba(255, 255, 255, 0.098);
                color: #ffffff;
            }
        }
    }
}

.bottom {
    width: 100%;
    position: fixed;
    bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 26rpx;

    .but {
        width: 100%;
        height: 80rpx;
        border-radius: 100rpx;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
