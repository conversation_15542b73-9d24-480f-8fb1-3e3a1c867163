<template>
    <ex-header title="购物车" background-color="#1a2b4a" text-color="#fff" :on-goback="goBack" mode="dark" />
    <view class="classbox"></view>
</template>

<script setup lang="ts">
import { onShareAppMessage, onShareTimeline, onShow } from '@dcloudio/uni-app'
import { useShare } from '@/composables/useShare'

// 使用全局分享功能 - 购物车页面跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: 'LNT(中国) - 购物车分享',
    imageUrl: '/static/logo.png',
    path: '/pages/shop/shopping',
    redirectToHome: true // 购物车页面分享后跳转到首页
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})

onShow(async () => {
    // 处理分享参数
    await handleShareParams()
})

const goBack = () => {
    uni.navigateBack()
}
</script>

<style scoped lang="scss">
.classbox {
    padding: 30rpx;
    box-sizing: border-box;
}
</style>
