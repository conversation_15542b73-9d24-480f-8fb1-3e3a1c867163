<script setup lang="ts">
import { ref } from 'vue'
import cityPicker from '@/uni_modules/piaoyi-cityPicker/components/piaoyi-cityPicker/piaoyi-cityPicker.vue'

// 响应式表单数据
const form = ref({
    name: '',
    number: '',
    address: '',
    xxdz: '',
    ismr: false
})

// 校验规则
const rules = {
    name: [
        {
            required: true,
            message: '请输入姓名',
            trigger: ['blur', 'change']
        }
    ],
    number: [
        {
            required: true,
            message: '请输入联系方式',
            trigger: ['blur', 'change']
        }
    ],
    address: [
        {
            required: true,
            message: '请选择地区',
            trigger: ['blur', 'change']
        }
    ],
    xxdz: [
        {
            required: true,
            message: '请输入详细地址',
            trigger: ['blur', 'change']
        }
    ]
}

// 表单引用
const uFormRef = ref(null)

const visible = ref(false)

// 提交方法
const submit = async () => {
    await uFormRef.value
        .validate()
        .then((valid): any => {
            if (valid) {
                uni.$u.toast('校验通过')
                console.log(form.value)
            } else {
                // uni.$u.toast('校验失败')
            }
        })
        .catch(() => {
            // 处理验证错误
            // uni.$u.toast('校验失败')
        })
}

const confirm = (e) => {
    form.value.address = e.name
    visible.value = false
}
</script>
<template>
    <view class="information">
        <ex-header title="新增收货地址" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <u-form
                :model="form"
                label-position="top"
                :rules="rules"
                :label-style="{ fontFamily: 'Source Han Sans', fontSize: '32rpx', color: '#FFFFFF', whiteSpace: 'nowrap' }"
                ref="uFormRef"
            >
                <u-form-item label="姓名" prop="name">
                    <u-input v-model="form.name" color="#FFFFFF" />
                </u-form-item>
                <u-form-item label="联系方式" prop="number">
                    <u-input v-model="form.number" color="#FFFFFF" />
                </u-form-item>
                <u-form-item label="选择地区" prop="address">
                    <u-input v-model="form.address" color="#FFFFFF" @focus="visible = true">
                        <template #suffix>
                            <image @click="visible = true" style="width: 60rpx; height: 60rpx" src="@/static/map.png" mode=""></image>
                        </template>
                    </u-input>
                    <cityPicker :column="3" :mask-close-able="true" @confirm="confirm" @cancel="visible = false" :visible="visible" />
                </u-form-item>
                <u-form-item label="详细地址" prop="xxdz">
                    <u-input v-model="form.xxdz" color="#FFFFFF" />
                </u-form-item>
                <u-form-item label="" prop="ismr">
                    <u-input disabled disabled-color="rgba(242, 242, 242, 0.2)">
                        <template #prefix>
                            <text class="mrtxt">设为默认地址</text>
                        </template>
                        <template #suffix>
                            <u-switch v-model="form.ismr"></u-switch>
                        </template>
                    </u-input>
                </u-form-item>
            </u-form>
        </view>
        <view class="butt">
            <u-button shape="circle" @click="submit">确认</u-button>
        </view>
    </view>
</template>

<style scoped lang="scss">
.information {
    .content {
        padding: 0 50rpx;

        :deep(.u-input) {
            border-radius: 20rpx;
            background: rgba(242, 242, 242, 0.2);
            border: 0 !important;
            height: 92rpx;
        }

        .mrtxt {
            font-family: Source Han Sans;
            font-size: 32rpx;
            color: #ffffff;
        }
    }

    .butt {
        position: fixed;
        bottom: 0;
        width: 100%;
        padding: 30rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
