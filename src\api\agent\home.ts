import { request } from '@/config/request'

export function home() {
    return request.http({
        url: '/api/d.agentIndex/info'
    })
}

export function count(data: { type: string; team_type: string }) {
    return request.http({
        url: '/api/d.homepage/indexCount',
        data
    })
}

export function myPurse(type: boolean) {
    return request.http({
        url: `/api/d.agentUser/${type ? 'IntegralOverview' : 'purse'}`
    })
}

export function transactionDetails(data: { page: number; limit: number; type: string }, type: boolean) {
    return request.http({
        url: `/api/d.agentUser/${type ? 'IntegralDetail' : 'transactionDetails'}`,
        data
    })
}

// 提现
export function agentApplyWithdrawal(data: { amount: number }, type: useType) {
    return request.http({
        url: type === 'client' ? '/api/y.clientUser/clientApplyWithdrawal' : '/api/d.agentUser/agentApplyWithdrawal',
        data
    })
}
