/// <reference types="vite/client" />
/// <reference types="@dcloudio/types/index" />
/// <reference types="uview-plus/types/index" />
/// <reference types="feng-uniapp-exploit/types/index" />

declare module '*.vue' {
    import { DefineComponent } from 'vue'
    const component: DefineComponent<{}, {}, any>
    export default component
}

declare module 'uview-plus'
declare module 'feng-uniapp-exploit'
