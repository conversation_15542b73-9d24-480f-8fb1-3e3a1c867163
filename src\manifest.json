{
    "name" : "LNT",
    "appid" : "__UNI__A5006F7",
    "description" : "LNT(中国)",
    "versionName" : "1.0.0",
    "versionCode" : 100,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0
        },
        "compatible" : {
            "ignoreVersion" : true
        },
        /* 模块配置 */
        "modules" : {
            "VideoPlayer" : {},
            "Share" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "minSdkVersion" : 21
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxd448c878a54da98d",
                        "UniversalLinks" : ""
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "name" : "amap_18648278829CcsBXVUm1",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "435934cdfcc901e4872a70391d92cccd",
                        "appkey_android" : "435934cdfcc901e4872a70391d92cccd"
                    }
                },
                "maps" : {
                    "amap" : {
                        "name" : "amap_18648278829CcsBXVUm1",
                        "appkey_ios" : "435934cdfcc901e4872a70391d92cccd",
                        "appkey_android" : "435934cdfcc901e4872a70391d92cccd"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : "",
                    "xxxhdpi" : ""
                }
            },
            "splashscreen" : {
                "iosStyle" : "common",
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : ""
                },
                "ios" : {
                    "storyboard" : "C:/Users/<USER>/Desktop/CustomStoryboard.zip"
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx4321371b2451ab5b",
        "setting" : {
            "urlCheck" : false,
            "es6" : true,
            "postcss" : false,
            "minified" : true
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3"
}
