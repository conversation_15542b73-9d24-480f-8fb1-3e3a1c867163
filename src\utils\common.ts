/**
 * 获取用户头像地址
 * <AUTHOR> <<EMAIL>>
 * @license MIT
 * @param { string } url 头像地址
 * @returns { string }
 */
export const getHeaderImage = (url?: string) => {
    if (url) {
        return import.meta.env.VITE_APP_BASE_URL + url
    } else {
        return 'http://cdn-pos.lingji.vip/static/system/headimg.png'
    }
}

/**
 * 获取图片地址
 * <AUTHOR> <<EMAIL>>
 * @license MIT
 * @param { string } url 图片地址
 * @returns { string }
 */
export const getImageUrl = (url?: string) => {
    return import.meta.env.VITE_APP_BASE_URL + url
}

/**
 * 富文本只要文字
 * <AUTHOR> <<EMAIL>>
 * @param { html } html 富文本
 * @returns { string }
 */
export const getPlainText = (html?: string) => {
    // 正则替换所有HTML标签（包括<img>等），保留换行符和空格
    return html
        ?.replace(/<[^>]+>/g, '')
        .replace(/\s+/g, ' ')
        .trim()
}

/**
 * 获取文件相对路径、URL
 * <AUTHOR> <<EMAIL>>
 * @license MIT
 * @param { string } fileName 文件名称 | 相对路径
 * @param { string } [filePath=images] filePath 文件在 /static 下的路径 获取URL时不传
 * @example
 * imgUrl('a.png')
 * imgUrl('a.png', '/public/')
 * imgUrl('/upload/images/img.png')
 * @returns {string}
 */
export const fileUrl = (fileName: string, filePath: string = 'images'): string => {
    if (/[ `!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/.test(fileName)) {
        return import.meta.env.VITE_APP_BASE_URL + fileName
    } else {
        return `http://cdn-pos.lingji.vip/static/${filePath}/${fileName}`
    }
}

/**
 * 获取指定日期的0时0分0秒的前七天23:59:59的时间戳
 * @param {string} dateStr - 日期字符串，格式为"YYYY-MM-DD"
 *  @returns {number} 时间戳（毫秒数）
 */
export const getSevenDaysBeforeCutoff = (dateStr: string) => {
    if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        throw new Error('日期格式不正确，请使用"YYYY-MM-DD"格式')
    }
    const parts = dateStr.split('-')
    const year = parseInt(parts[0], 10)
    const month = parseInt(parts[1], 10) - 1
    const day = parseInt(parts[2], 10)
    const specifiedDate = new Date(year, month, day)
    specifiedDate.setHours(0, 0, 0, 0)
    const sevenDaysBefore = new Date(specifiedDate)
    sevenDaysBefore.setDate(specifiedDate.getDate() - 7)
    sevenDaysBefore.setHours(23, 59, 59, 0)
    return sevenDaysBefore.getTime()
}

// 综合转换函数（支持自定义格式）
export const convertTimestamp = (timestamp: string | number | Date): string => {
    const date = new Date(timestamp * 1000)
    const year = date.getFullYear()
    const month = ('0' + (date.getMonth() + 1)).slice(-2)
    const day = ('0' + date.getDate()).slice(-2)
    const hour = ('0' + date.getHours()).slice(-2)
    const minute = ('0' + date.getMinutes()).slice(-2)
    const second = ('0' + date.getSeconds()).slice(-2)
    const formattedTime = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second
    return formattedTime
}

/**
 * 验证是否为空对象
 * <AUTHOR> <<EMAIL>>
 * @param { object } obj
 * @returns  { boolean }
 */
export const isEmptyObject = (obj: object) => {
    return (
        obj && // 确保不是 null 或 undefined
        typeof obj === 'object' && // 确保是对象
        !Array.isArray(obj) && // 确保不是数组
        Object.keys(obj).length === 0 // 确保没有属性
    )
}
