<script setup lang="ts">
import { myCert, myCertSub } from '@/api/mine/user'
import { getImageUrl } from '@/utils/common'
import { roleInfo, uploadFile } from '@/api/common/common'
const orderListRef = ref()
const doSearch = (formData: { page: number; limit: number }, onSuccess: Function) => {
    const submitData = { ...formData }
    myCert(submitData).then((res) => {
        const { data } = res as { data: { data: any; total: number } }
        onSuccess({ data })
    })
}

const downloadCertificate = (url: string) => {
    uni.getImageInfo({
        src: getImageUrl(url),
        success: (res) => {
            uni.saveImageToPhotosAlbum({
                filePath: res.path,
                success: () => {
                    uni.showToast({
                        title: '证书保存成功',
                        icon: 'success',
                        duration: 1000
                    })
                },
                fail: () => {
                    uni.showToast({
                        title: '证书保存失败',
                        icon: 'none',
                        duration: 1000
                    })
                }
            })
        }
    })
}

const showPopup = ref(false)
const form = ref()
const formData = reactive({
    title: '',
    num: '',
    role_id: '',
    certificate_path: ''
})
const roleList = ref<{ role_id: number; name: string }[]>([])
const isShowSheet = ref(false)
const getRoleInfo = () => {
    roleInfo().then((res: any) => {
        roleList.value = res.data
    })
}
const selectRole = (item: any) => {
    formData.role_id = item.role_id
}
const getRoleName = computed(() => {
    const role = roleList.value.find((item: any) => item.role_id === formData.role_id)
    return role ? role.name : ''
})
const rules = {
    title: {
        type: 'string',
        required: true,
        message: '请输入证书名称',
        trigger: ['blur', 'change']
    },
    num: {
        type: 'string',
        required: true,
        message: '请输入证书编号',
        trigger: ['blur', 'change']
    },
    role_id: {
        type: 'number',
        required: true,
        message: '请选择证书等级',
        trigger: ['blur', 'change']
    },
    certificate_path: {
        type: 'string',
        required: true,
        message: '请上传证书',
        trigger: ['blur', 'change']
    }
}
const fileList = ref<any[]>([])
const deletePic = (event: { index: number }) => {
    formData.certificate_path = ''
    fileList.value.splice(event.index, 1)
}
const afterRead = async (event: any) => {
    const lists: any = [].concat(event.file)
    let fileListLen = fileList.value.length
    lists.map((item: any) => {
        fileList.value.push({
            ...item,
            status: 'uploading',
            message: '上传中'
        })
    })
    for (let i = 0; i < lists.length; i++) {
        const result: any = await uploadFile(lists[i].url)
        let item = fileList.value[fileListLen]
        formData.certificate_path = result.data.url
        fileList.value.splice(
            fileListLen,
            1,
            Object.assign(item, {
                status: 'success',
                message: '',
                url: getImageUrl(result.data.url),
                urls: result.data.url
            })
        )
        fileListLen++
    }
}

const submit = () => {
    console.log(1231231, formData)
    form.value
        .validate()
        .then((valid: boolean) => {
            if (valid) {
                // uni.showToast({ title: '校验成功', icon: 'none', mask: true })
                myCertSub(formData).then(() => {
                    uni.showToast({ title: '提交成功', icon: 'none', mask: true })
                    setTimeout(() => {
                        showPopup.value = false
                        orderListRef.value.refreshFn()
                    }, 1500)
                })
            } else {
                // uni.showToast({ title: '校验失败1', icon: 'none', mask: true })
            }
        })
        .catch(() => {
            // 处理验证错误
            // uni.showToast({ title: '校验失败2', icon: 'none', mask: true })
        })
}

onMounted(() => {
    getRoleInfo()
})
</script>
<template>
    <view class="certificate">
        <ex-header title="我的证书" background-color="#1a2b4a" text-color="#fff" mode="dark" />
        <view class="content">
            <ex-list ref="orderListRef" :on-form-search="doSearch">
                <template v-slot="{ row }">
                    <view class="items">
                        <view class="flex-center-between timer">
                            <view class="left">
                                <image src="@/static/timer.png" mode="widthFix" />
                                <text>{{ row.create_time.slice(0, 10) }}</text>
                            </view>
                            <view v-if="row.status === 3" class="right" @tap="downloadCertificate(row.certificate_path)">下载</view>
                            <view
                                class="right"
                                :style="{ color: row.status === 1 ? '#F59B21' : '#b81b22' }"
                                v-if="row.status === 1 || row.status === 2"
                            >
                                {{ row.status_msg }}
                            </view>
                        </view>
                        <view class="title">{{ row.title || '暂无名称' }}</view>
                        <view class="desc">编号：{{ row.num || '暂无编号' }}</view>
                        <view class="desc">认证等级：{{ row.course_role_name || '暂无等级' }}</view>
                        <ex-image :src="getImageUrl(row.certificate_path)" width="100%" height="100%" :is-icon="true" mode="widthFix" />
                    </view>
                </template>
            </ex-list>
        </view>
        <u-popup :show="showPopup" :round="10" @close="showPopup = false" mode="center">
            <view class="popup">
                <view class="title">等级认证</view>
                <u-form ref="form" :model="formData" :rules="rules" label-position="top" label-width="auto">
                    <u-form-item label="证书名称" prop="title" required>
                        <u-input v-model="formData.title" border="bottom" placeholder="请输入证书名称" clearable />
                    </u-form-item>
                    <u-form-item label="证书编号" prop="num" required>
                        <u-input v-model="formData.num" border="bottom" placeholder="请输入证书编号" clearable />
                    </u-form-item>
                    <u-form-item label="认证等级" prop="role_id" required @tap="isShowSheet = true">
                        <u-input :model-value="getRoleName" border="bottom" readonly placeholder="请选择认证等级" />
                    </u-form-item>
                    <u-form-item label="上传证书" prop="certificate_path" required>
                        <u-upload
                            accept="image"
                            :file-list="fileList"
                            @afterRead="afterRead"
                            @delete="deletePic"
                            multiple
                            :max-count="1"
                            width="60"
                            height="60"
                        />
                    </u-form-item>
                    <u-form-item>
                        <u-button type="primary" @tap="submit">完成</u-button>
                    </u-form-item>
                </u-form>
            </view>
        </u-popup>
        <u-action-sheet
            :actions="roleList"
            :title="'认证等级'"
            :show="isShowSheet"
            close-on-click-overlay
            cancel-text="取消"
            @close="isShowSheet = false"
            @select="selectRole"
        />
        <view class="buttons">
            <u-button class="buttons" @tap="showPopup = true">上传</u-button>
        </view>
    </view>
</template>

<style scoped lang="scss">
.certificate {
    padding-bottom: 80rpx;

    .content {
        padding: 30rpx;

        .items {
            padding: 30rpx;
            border-radius: 18rpx;
            background-color: #2f3e5a;
            margin-bottom: 30rpx;

            .timer {
                image {
                    width: 50rpx;
                    height: 50rpx;
                    margin-right: 20rpx;
                    vertical-align: middle;
                }

                text {
                    font-size: 28rpx;
                    color: #ffffff;
                    vertical-align: middle;
                }

                .right {
                    font-size: 28rpx;
                    color: #8cc046;
                }
            }

            .title {
                text-align: center;
                font-size: 32rpx;
                color: #ffffff;
                margin-top: 12rpx;
            }

            .desc {
                text-align: center;
                font-size: 28rpx;
                color: #ffffff;
                margin-top: 10rpx;
            }

            :deep(.ex-images) {
                margin-top: 30rpx;
            }
        }
    }

    .buttons {
        :deep(.u-button) {
            width: 50vw;
            position: fixed;
            bottom: 30rpx;
            left: 0;
            right: 0;
            margin: 0 auto;
            border-radius: 18rpx;
        }
    }

    .popup {
        width: 80vw;
        padding: 30rpx 40rpx;

        .title {
            font-weight: 700;
            text-align: center;
            font-size: 32rpx;
            color: #3d3d3d;
        }
    }
}
</style>
