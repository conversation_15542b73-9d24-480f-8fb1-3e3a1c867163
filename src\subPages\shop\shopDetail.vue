<template>
    <u-navbar
        title="商品详情"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <image class="pic" :src="getImageUrl(shopdata?.sku_image)" mode="aspectFill"></image>
        <view class="head">
            <text class="name">{{ shopdata?.goods?.goods_name }}</text>
            <view class="pricep">
                <text class="price">￥{{ shopdata?.market_price }}</text>
                <text class="hyprice">会员价：{{ shopdata?.price }}</text>
            </view>
        </view>
        <view class="numbox">
            <u-number-box v-model="addshopparams.num" button-size="28"></u-number-box>
        </view>
        <view class="yf">
            <text class="name">运费</text>
            <text class="name" v-if="shopdata?.goods?.is_free_shipping === 1">免运费</text>
            <text class="name" v-else>{{ shopdata?.goods?.delivery_money }}</text>
        </view>
        <view class="txtpart">
            <text class="spxq">商品详情</text>
            <mp-html :content="shopdata?.goods?.goods_desc" />
        </view>
    </view>
    <view class="bottom">
        <image class="kf" src="@/static/kficon.png" mode=""></image>
        <view class="but1" @click="addshop">加入购物车</view>
        <view class="but2" @click="submit">立即购买</view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { goodsSku, addCart } from '@/api/shop/index'
import { getImageUrl } from '@/utils/common'

const shopdata = ref({})
const submit = () => {
    uni.navigateTo({
        url: '/subPages/shop/productOrder'
    })
}

const addshopparams = ref({
    goods_id: 0,
    sku_id: 0,
    num: 1
})
const addshop = async () => {
    await addCart(addshopparams.value).then((res: any) => {
        if (res.code === 1) {
            uni.showToast({
                title: res.msg,
                icon: 'success'
            })
        }
    })
}

onLoad(async (e: any) => {
    await goodsSku(e.id).then((res: any) => {
        res.data.goods.goods_desc = res.data.goods.goods_desc
            .replace(/<img/g, '<img style="width: 100%; max-width: 100%; height: auto;"')
            .replace(/<p([^>]*)>/g, function (match, p1) {
                if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                    // 如果已有background-color定义，先移除再追加（避免重复）
                    return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
                } else if (/style\s*=\s*['"]/.test(p1)) {
                    // 有style但无background-color，直接在末尾追加
                    return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
                } else {
                    // 无style属性，直接添加
                    return '<p' + p1 + ' style="background-color: transparent!important;">'
                }
            })
            .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
        shopdata.value = res.data
        addshopparams.value.goods_id = res.data.goods_id
        addshopparams.value.sku_id = res.data.sku_id
    })
})
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    display: grid;
    align-content: flex-start;
    padding: 10rpx 32rpx 200rpx 32rpx;
    box-sizing: border-box;

    .pic {
        width: 100%;
        height: 440rpx;
    }

    .head {
        margin-top: 40rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
            max-width: 60%;
            font-family: Source Han Sans;
            font-size: 40rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .pricep {
            display: grid;
            justify-items: flex-end;

            .price {
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                text-align: center;
                display: flex;
                align-items: center;
                letter-spacing: normal;
                color: #ffffff;
                text-decoration: line-through;
            }

            .hyprice {
                margin-top: 10rpx;
                font-family: Source Han Sans;
                font-size: 28rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }
    }

    .numbox {
        margin-top: 30rpx;
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }

    .yf {
        margin-top: 30rpx;
        width: 100%;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 0.2);
        padding: 10rpx 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .name {
            font-family: Source Han Sans;
            font-size: 32rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }
    }

    .txtpart {
        display: grid;
        padding: 30rpx 34rpx;

        .spxq {
            margin-bottom: 20rpx;
            font-family: Source Han Sans;
            font-size: 32rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .xq {
            margin-top: 24rpx;
            font-family: Source Han Sans;
            font-size: 28rpx;
            font-weight: normal;
            line-height: 50rpx;
            letter-spacing: normal;
            color: #ffffff;
        }

        .img {
            width: 95%;
            height: 370rpx;
            margin-top: 10rpx;
        }
    }
}

.bottom {
    width: 100%;
    position: fixed;
    bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a2b4a;
    padding-top: 30rpx;

    .kf {
        width: 60rpx;
        height: 60rpx;
    }

    .but1 {
        margin-left: 16rpx;
        width: 40%;
        height: 80rpx;
        border-radius: 100rpx;
        background: #25a3dd;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        color: #ffffff;
    }

    .but2 {
        margin-left: 16rpx;
        width: 40%;
        height: 80rpx;
        border-radius: 100rpx;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #1a2b4a;
    }
}
</style>
