<template>
    <u-navbar
        title="商品订单"
        bg-color="#0E2942"
        left-icon-color="#FFFFFF"
        auto-back
        :title-style="{ fontFamily: 'Source Han Sans', fontSize: '36rpx', color: '#FFFFFF' }"
    ></u-navbar>
    <view class="classbox">
        <view class="adress">
            <text class="xzshdz" @click="gochangeaddress">选择收货地址</text>
            <image class="add" src="@/static/add.png" mode="" @click="goaddress"></image>
        </view>
        <view class="shoplist">
            <view class="shopone" v-for="(item, index) in shopList" :key="index">
                <image class="img" :src="item.img" mode=""></image>
                <view class="rightp">
                    <view class="topp">
                        <text class="name">{{ item.name }}</text>
                        <text class="num">x{{ item.num }}</text>
                    </view>
                    <view class="bottomp">
                        <view class="bqlist">
                            <view class="bq" v-for="(ele, ind) in item.bqlist" :key="ind">
                                {{ ele.name }}
                            </view>
                        </view>
                        <text class="yprice">￥{{ item.yprice }}</text>
                        <text class="price">￥{{ item.price }}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="zkou">
            <view class="head">
                <text class="title">商品金额</text>
                <text class="price">￥4850</text>
            </view>
            <view class="zkpri">
                <view class="leftp">
                    <image class="icon" src="@/static/zk.png" mode=""></image>
                    <text class="title">L3会员专享折扣</text>
                </view>
                <text class="yhpri">-1455</text>
            </view>
            <view class="henx"></view>
            <text class="hej">合计 ￥7760</text>
        </view>
        <view class="zhif">
            <text class="zftxt">支付方式</text>
            <view class="henx"></view>
            <view class="zfp">
                <view class="tip">
                    <image class="icon" src="@/static/wxzf.png" mode=""></image>
                    <text class="title">微信支付</text>
                </view>
                <image class="ricon" src="@/static/yes.png" mode=""></image>
            </view>
        </view>
    </view>
    <view class="bottom">
        <view class="but1" @click="submit">支付</view>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const shopList = ref([
    {
        img: '/static/sl1.png',
        name: '马克杯',
        num: '1',
        bqlist: [
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            }
        ],
        yprice: '100.00',
        price: '485'
    },
    {
        img: '/static/sl1.png',
        name: '马克杯',
        num: '1',
        bqlist: [
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            },
            {
                id: 1,
                name: '黑色'
            }
        ],
        yprice: '100.00',
        price: '485'
    }
])

const goaddress = () => {
    uni.navigateTo({
        url: '/subPages/shop/addAddress'
    })
}

const gochangeaddress = () => {
    uni.navigateTo({
        url: '/subPages/shop/changeAddress'
    })
}

const submit = () => {
    console.log()
}
</script>

<style scoped lang="scss">
.classbox {
    margin-top: 174rpx;
    width: 100vw;
    display: grid;
    align-content: flex-start;
    padding: 10rpx 32rpx 200rpx 32rpx;
    box-sizing: border-box;

    .adress {
        margin-top: 20rpx;
        width: 100%;
        padding: 50rpx 30rpx;
        border-radius: 20rpx;
        background: rgba(220, 220, 220, 0.1);
        box-sizing: border-box;
        border: 2rpx solid #a3a3a3;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .xzshdz {
            font-family: Source Han Sans;
            font-size: 36rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }

        .add {
            width: 70rpx;
            height: 70rpx;
        }
    }

    .shoplist {
        width: 100%;
        display: grid;

        .shopone {
            margin-top: 20rpx;
            width: 100%;
            padding: 50rpx 30rpx;
            border-radius: 20rpx;
            background: rgba(220, 220, 220, 0.1);
            box-sizing: border-box;
            border: 2rpx solid #a3a3a3;
            display: flex;
            align-items: center;

            .img {
                width: 218rpx;
                height: 100%;
            }

            .rightp {
                width: 100%;
                display: grid;
                margin-left: 12rpx;

                .topp {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .name {
                        font-family: Source Han Sans;
                        font-size: 32rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #ffffff;
                    }

                    .num {
                        font-family: Source Han Sans;
                        font-size: 28rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #ffffff;
                    }
                }

                .bottomp {
                    display: flex;
                    margin-top: 44rpx;

                    .bqlist {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 20rpx;

                        .bq {
                            margin-top: 10rpx;
                            padding: 4rpx 10rpx;
                            border-radius: 30rpx;
                            background-color: #ffffff;
                            font-family: Source Han Sans;
                            font-size: 24rpx;
                            font-weight: normal;
                            line-height: normal;
                            text-align: center;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            letter-spacing: normal;
                            color: #25a3dd;
                            white-space: nowrap;
                        }
                    }

                    .yprice {
                        margin-top: 20rpx;
                        margin-left: 16rpx;
                        font-family: Source Han Sans;
                        font-size: 28rpx;
                        font-weight: normal;
                        line-height: normal;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        letter-spacing: normal;
                        color: #ffffff;
                        text-decoration: line-through;
                    }

                    .price {
                        margin-left: 10rpx;
                        font-family: Source Han Sans;
                        font-size: 36rpx;
                        font-weight: normal;
                        line-height: normal;
                        letter-spacing: normal;
                        color: #f6a538;
                    }
                }
            }
        }
    }

    .zkou {
        margin-top: 20rpx;
        width: 100%;
        padding: 30rpx 30rpx;
        border-radius: 20rpx;
        background: rgba(220, 220, 220, 0.1);
        box-sizing: border-box;
        border: 2rpx solid #a3a3a3;
        display: grid;

        .head {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .title {
                font-family: Source Han Sans;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }

            .price {
                font-family: Source Han Sans;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #ffffff;
            }
        }

        .zkpri {
            margin-top: 26rpx;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .leftp {
                display: flex;
                .icon {
                    width: 30rpx;
                    height: 32rpx;
                }
                .title {
                    margin-left: 10rpx;
                    font-family: Source Han Sans;
                    font-size: 32rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }
            .yhpri {
                font-family: Source Han Sans;
                font-size: 32rpx;
                font-weight: normal;
                line-height: normal;
                letter-spacing: normal;
                color: #d9001b;
            }
        }
        .henx {
            width: 100%;
            border-bottom: 2rpx solid rgba(242, 242, 242, 0.2706);
            margin-top: 20rpx;
        }
        .hej {
            margin-top: 26rpx;
            font-family: Source Han Sans;
            font-size: 36rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
            text-align: end;
        }
    }
    .zhif {
        margin-top: 20rpx;
        width: 100%;
        padding: 30rpx 30rpx;
        border-radius: 20rpx;
        background: rgba(220, 220, 220, 0.1);
        box-sizing: border-box;
        border: 2rpx solid #a3a3a3;
        display: grid;
        .zftxt {
            font-family: Source Han Sans;
            font-size: 32rpx;
            font-weight: normal;
            line-height: normal;
            letter-spacing: normal;
            color: #ffffff;
        }
        .henx {
            width: 100%;
            border-bottom: 2rpx solid rgba(242, 242, 242, 0.2706);
            margin-top: 20rpx;
        }
        .zfp {
            width: 100%;
            margin-top: 34rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .tip {
                display: flex;
                align-items: center;
                .icon {
                    width: 50rpx;
                    height: 50rpx;
                }
                .title {
                    font-family: Source Han Sans;
                    font-size: 32rpx;
                    font-weight: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    color: #ffffff;
                }
            }
            .ricon {
                width: 50rpx;
                height: 50rpx;
            }
        }
    }
}

.bottom {
    width: 100%;
    position: fixed;
    bottom: 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1a2b4a;
    padding-top: 30rpx;

    .but1 {
        width: 90%;
        height: 80rpx;
        border-radius: 100rpx;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: Source Han Sans;
        font-size: 26rpx;
        font-weight: normal;
        line-height: normal;
        letter-spacing: normal;
        color: #1a2b4a;
    }
}
</style>
