import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import AutoImport from 'unplugin-auto-import/vite'

import path from 'path'
const pathSrc = path.resolve(__dirname, 'src')

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    const { VITE_APP_BASE_URL, VITE_APP_BASE_PRE } = loadEnv(mode, process.cwd())

    return {
        transpileDependencies: ['uview-plus'],
        resolve: {
            // 别名
            alias: {
                '@': path.join(__dirname, './src')
            }
        },
        plugins: [
            uni(),
            AutoImport({
                // imports: ['vue', 'uni-app', { 'feng-uniapp-exploit': ['default'] }, { 'src/utils': ['default'] }],
                imports: ['vue', 'uni-app'],
                vueTemplate: true,
                dts: path.resolve(pathSrc, 'types', 'auto-imports.d.ts')
            })
        ],
        // build: {
        //     rollupOptions: {
        //         external: ['feng-exploit-plus']
        //     }
        // },
        server: {
            proxy: {
                [VITE_APP_BASE_PRE]: {
                    target: VITE_APP_BASE_URL,
                    ws: true,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(new RegExp('^' + VITE_APP_BASE_PRE), '')
                }
            }
        }
    }
})
