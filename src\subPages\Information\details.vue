<script setup lang="ts">
import { getImageUrl } from '@/utils/common'
import { collectDet, isCollect, collect, removeCollect } from '@/api/mine/user'
import useUserStore from '@/store/user'
import { useShare } from '@/composables/useShare'
const userStore = useUserStore()

// 使用全局分享功能 - 资讯详情页不跳转到首页
const { handleShareParams, getShareAppMessageConfig, getShareTimelineConfig } = useShare({
    title: '精彩资讯分享',
    imageUrl: '/static/logo.png',
    path: '/subPages/Information/details',
    redirectToHome: false, // 资讯详情页不跳转到首页
    // 动态获取分享标题
    getTitleFn: () => details.value.title || '精彩资讯分享',
    // 动态获取分享图片
    getImageUrlFn: () => (details.value.pic_path ? getImageUrl(details.value.pic_path) : getImageUrl('upload/file/logo.jpg')),
    // 动态获取额外查询参数（资讯ID）
    getExtraQueryFn: () => ({ id: details.value.id })
})

// 设置分享给朋友功能
onShareAppMessage(() => {
    return getShareAppMessageConfig()
})

// 设置分享到朋友圈功能
onShareTimeline(() => {
    return getShareTimelineConfig()
})
// 定义文章详情的接口类型
interface ArticleDetails {
    id: number
    title: string
    content: string
    pic_path: string
    create_time: string
    [key: string]: any
}

// 响应式数据
const details = ref<ArticleDetails>({} as ArticleDetails)
const loading = ref(true)
const error = ref('')

// 处理图片样式的函数
const processImageContent = (content: string): string => {
    if (!content) return ''
    return content
        .replace(/<img/g, `<img style="width:100%; max-width: 100%;"`)
        .replace(/<p([^>]*)>/g, function (match, p1) {
            if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                // 如果已有background-color定义，先移除再追加（避免重复）
                return '<p' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
            } else if (/style\s*=\s*['"]/.test(p1)) {
                // 有style但无background-color，直接在末尾追加
                return '<p' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
            } else {
                // 无style属性，直接添加
                return '<p' + p1 + ' style="background-color: transparent!important;">'
            }
        })
        .replace(/<span([^>]*)>/g, function (match, p1) {
            if (/style\s*=\s*['"][^'"]*background-color\s*:[^'"]*['"]/i.test(p1)) {
                // 如果已有background-color定义，先移除再追加（避免重复）
                return '<span' + p1.replace(/(style\s*=\s*['"])([^'"]*)(['"])/i, '$1$2; background-color: transparent!important;$3') + '>'
            } else if (/style\s*=\s*['"]/.test(p1)) {
                // 有style但无background-color，直接在末尾追加
                return '<span' + p1.replace(/(style\s*=\s*['"][^'"]*)(['"])/, '$1; background-color: transparent!important;$2') + '>'
            } else {
                // 无style属性，直接添加
                return '<span' + p1 + ' style="background-color: transparent!important;">'
            }
        })
        .replace(/<video/g, '<video style="width: 100%; max-width: 100%; height: auto;"')
}
// 加载文章详情
const loadArticleDetails = async (id: string) => {
    try {
        loading.value = true
        error.value = ''

        const response = await collectDet(Number(id))
        const { data } = response as { data: ArticleDetails }

        // 处理内容中的图片样式，不直接修改原数据
        const processedData = {
            ...data,
            content: processImageContent(data.content)
        }

        details.value = processedData
    } catch (err) {
        console.error('加载文章详情失败:', err)
        error.value = '加载失败，请重试'
        uni.showToast({
            title: '加载失败',
            icon: 'none',
            duration: 2000
        })
    } finally {
        loading.value = false
    }
}

const isCollectValue = ref(false)

// 获取是否为收藏状态
const getCollectStatus = async () => {
    try {
        const response = await isCollect({ item_id: details.value.id })
        const { data } = response as { data: boolean }
        isCollectValue.value = data
    } catch (err) {
        console.error('获取收藏状态失败:', err)
        // 静默失败，不影响用户体验
    }
}

// 重试加载
const retryLoad = (id: string) => {
    loadArticleDetails(id)
}

// 图片加载错误处理
const onImageError = () => {
    console.warn('图片加载失败')
    uni.showToast({
        title: '图片加载失败',
        icon: 'none',
        duration: 1500
    })
}

// 处理收藏/取消收藏
const handleCollectClick = async () => {
    try {
        if (isCollectValue.value) {
            // 当前已收藏，执行取消收藏
            await removeCollect({ item_id: details.value.id })
            isCollectValue.value = false
            uni.showToast({ title: '取消收藏成功', icon: 'none', mask: true })
        } else {
            // 当前未收藏，执行收藏
            await collect({ item_id: details.value.id })
            isCollectValue.value = true
            uni.showToast({ title: '收藏成功', icon: 'none', mask: true })
        }
    } catch (err) {
        const action = isCollectValue.value ? '取消收藏' : '收藏'
        uni.showToast({ title: `${action}失败`, icon: 'none', mask: true })
        console.error(`${action}失败:`, err)
    }
}

// 处理分享按钮点击
const handleShareClick = (event: Event) => {
    event.stopPropagation() // 阻止事件冒泡
}

onLoad(async ({ id }: { id: string }) => {
    if (!id) {
        error.value = '缺少文章ID'
        loading.value = false
        return
    }

    await loadArticleDetails(id)

    // 加载完文章详情后，如果用户已登录则获取收藏状态
    if (userStore.checkLogin && details.value.id) {
        await getCollectStatus()
    }
})

onShow(async () => {
    // 处理分享参数（推荐人逻辑）
    await handleShareParams()

    // 只有在用户登录且文章详情已加载的情况下才获取收藏状态
    if (userStore.checkLogin && details.value.id && !loading.value) {
        await getCollectStatus()
    }
})
</script>
<template>
    <view class="details">
        <ex-header title="资讯详情" background-color="#1a2b4a" text-color="#fff" mode="dark" />

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <uni-load-more status="loading" />
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
            <view class="error-message">{{ error }}</view>
            <button class="retry-btn" @click="retryLoad($route.query.id)">重试</button>
        </view>

        <!-- 内容区域 -->
        <view v-else class="content">
            <image v-if="details.pic_path" class="pic" :src="getImageUrl(details.pic_path)" mode="widthFix" @error="onImageError" />
            <view class="tit">{{ details.title || '暂无标题' }}</view>
            <view class="timer">发布时间：{{ details.create_time || '未知时间' }}</view>
            <mp-html v-if="details.content" class="desc" :content="details.content" />
            <view v-else class="no-content">暂无内容</view>
            <view class="flex-center-end tools_box">
                <image :src="`/static/${isCollectValue ? 'star_act' : 'starf'}.png`" mode="widthFix" @tap="handleCollectClick" />
                <button class="flex share-btn" open-type="share" @tap.stop="handleShareClick">
                    <image src="@/static/shares.png" mode="widthFix" />
                </button>
            </view>
        </view>
    </view>
</template>

<style scoped lang="scss">
.details {
    min-height: 100vh;

    // 加载状态样式
    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 100rpx 0;
        color: #fff;
    }

    // 错误状态样式
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 100rpx 30rpx;

        .error-message {
            color: #ff6b6b;
            font-size: 28rpx;
            margin-bottom: 40rpx;
            text-align: center;
        }

        .retry-btn {
            background-color: #1a2b4a;
            color: #fff;
            border: none;
            border-radius: 8rpx;
            padding: 20rpx 40rpx;
            font-size: 28rpx;

            &:active {
                background-color: #0f1a2e;
            }
        }
    }

    // 内容区域样式
    .content {
        padding: 30rpx 30rpx 100rpx;
        color: #fff;

        .pic {
            width: 100%;
            border-radius: 18rpx;
            margin-bottom: 20rpx;
        }

        .tit {
            margin: 26rpx 0 20rpx;
            font-size: 32rpx;
            letter-spacing: 1px;
            text-align: center;
            font-weight: 700;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }

        .timer {
            margin-bottom: 30rpx;
            font-size: 24rpx;
            text-align: center;
            color: #ffffff;
            opacity: 0.8;
        }

        .desc {
            font-size: 28rpx;
            line-height: 1.6;
            color: #ffffff;

            // 富文本内容样式优化
            :deep(img) {
                max-width: 100% !important;
                height: auto !important;
                border-radius: 8rpx;
                margin: 10rpx 0;
            }
        }

        .no-content {
            text-align: center;
            color: #999;
            font-size: 28rpx;
            padding: 60rpx 0;
        }

        .tools_box {
            padding: 20rpx 30rpx;
            background-color: rgba($color: #44526a, $alpha: 0.8);
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0 auto;

            image {
                margin-right: 30rpx;
                width: 50rpx;

                &:last-child {
                    margin-right: 0;
                }
            }

            button {
                width: fit-content;
                margin: 0;
                padding: 0;
                background: transparent;
                border: none;

                &.share-btn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                &::after {
                    border: none;
                }
            }
        }
    }
}
</style>
